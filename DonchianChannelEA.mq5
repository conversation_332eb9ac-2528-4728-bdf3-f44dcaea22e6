//+------------------------------------------------------------------+
//|                                        DonchianChannelEA.mq5     |
//|                                                                  |
//|                      Donchian Channel Midline Crossover EA      |
//+------------------------------------------------------------------+
#property copyright ""
#property link      ""
#property version   "1.00"

#include <Trade\Trade.mqh>

//--- Input parameters
input int    DonchianPeriod = 20;        // Donchian Channel Period
input int    DonchianShift = 6;          // Donchian Channel Shift (offset)
input double TakeProfitPips = 2.0;       // Take Profit in pips
input double LotSize = 0.10;             // Lot size
input string TradeComment = "DC_EA";     // Trade comment
input int    StartHour = 10;             // Trading start hour (0-23)
input int    EndHour = 18;               // Trading end hour (0-23)
input int    RSIPeriod = 14;             // RSI Period
input ENUM_APPLIED_PRICE RSIPrice = PRICE_CLOSE; // RSI Applied Price

//--- Global variables
CTrade trade;
datetime lastBarTime = 0;
datetime lastTradeTime = 0;              // Track last trade time to prevent multiple trades per bar
double pipValue;
int rsiHandle;                           // RSI indicator handle
ulong breakevenPositions[];              // Array to track positions moved to breakeven
ulong partialClosedPositions[];          // Array to track positions with partial close
ulong hedgedPositions[];                 // Array to track positions that have been hedged
ulong trailingPositions[];               // Array to track positions with trailing stops
double trailingHighWater[];              // Array to track highest/lowest prices for trailing

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Calculate pip value based on broker's digit precision
    if(_Digits == 5 || _Digits == 3)
        pipValue = 10 * _Point;  // 5-digit broker
    else
        pipValue = _Point;       // 4-digit broker

    // Initialize RSI indicator
    rsiHandle = iRSI(_Symbol, PERIOD_CURRENT, RSIPeriod, RSIPrice);
    if(rsiHandle == INVALID_HANDLE)
    {
        Print("Failed to create RSI indicator handle");
        return(INIT_FAILED);
    }

    Print("Donchian Channel EA initialized. Pip value: ", pipValue);
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release RSI indicator handle
    if(rsiHandle != INVALID_HANDLE)
        IndicatorRelease(rsiHandle);

    Print("Donchian Channel EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Monitor existing positions for trailing stop management
    ManageTrailingStops();

    // Monitor positions for advanced trailing stops (>10 pip TP)
    ManageAdvancedTrailing();

    // Monitor positions for hedging opportunities
    ManageHedging();

    // Check if new bar has formed for signal generation
    if(!IsNewBar())
        return;

    // Check if current time is within trading hours
    if(!IsWithinTradingHours())
    {
        // Close all positions outside trading hours
        //CloseAllPositions();
        return;
    }
    
    // Calculate Donchian midline values for the last 3 bars
    double midline0 = CalculateDonchianMidline(0);
    double midline1 = CalculateDonchianMidline(1);
    double midline2 = CalculateDonchianMidline(2);
    double midline3 = CalculateDonchianMidline(3);

    // Get open prices for the last 3 bars
    double open0 = iOpen(_Symbol, PERIOD_CURRENT, 0);
    double open1 = iOpen(_Symbol, PERIOD_CURRENT, 1);
    double open2 = iOpen(_Symbol, PERIOD_CURRENT, 2);
    double open3 = iOpen(_Symbol, PERIOD_CURRENT, 3);

    // Get RSI value for current bar
    double rsiValue = GetRSIValue(1); // Use previous completed bar
    if(rsiValue == EMPTY_VALUE)
        return; // Skip if RSI data not available

    // Check for buy signal (Donchian crossover + RSI > 50)
    bool buySignal = false;
    if(open0 > midline0 && open1 > midline1 && open0 > open1 && rsiValue > 50.0)
    {
        if(open2 < midline2 || open3 < midline3)
            buySignal = true;
    }

    // Check for sell signal (Donchian crossover + RSI < 50)
    bool sellSignal = false;
    if(open0 < midline0 && open1 < midline1 && open0 < open1 && rsiValue < 50.0)
    {
        if(open2 > midline2 || open3 > midline3)
            sellSignal = true;
    }

    // Execute trades based on signals (only if no trade opened this bar)
    datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    if(lastTradeTime < currentBarTime)
    {
        if(buySignal)
        {
            //CloseOppositePositions(ORDER_TYPE_SELL);
            if(OpenBuyTrade())
                lastTradeTime = currentBarTime;
        }
        else if(sellSignal)
        {
            //CloseOppositePositions(ORDER_TYPE_BUY);
            if(OpenSellTrade())
                lastTradeTime = currentBarTime;
        }
    }
}

//+------------------------------------------------------------------+
//| Check if new bar has formed                                     |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime currentBarTime = iTime(_Symbol, PERIOD_CURRENT, 0);
    if(currentBarTime != lastBarTime)
    {
        lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if current time is within trading hours                   |
//+------------------------------------------------------------------+
bool IsWithinTradingHours()
{
    MqlDateTime timeStruct;
    TimeToStruct(TimeCurrent(), timeStruct);
    int currentHour = timeStruct.hour;

    // Check if current hour is within trading range
    if(StartHour <= EndHour)
    {
        // Normal case: e.g., 10:00 to 18:00
        return (currentHour >= StartHour && currentHour < EndHour);
    }
    else
    {
        // Overnight case: e.g., 22:00 to 06:00
        return (currentHour >= StartHour || currentHour < EndHour);
    }
}

//+------------------------------------------------------------------+
//| Calculate Donchian Channel midline for specified bar            |
//+------------------------------------------------------------------+
double CalculateDonchianMidline(int barIndex)
{
    // Find highest and lowest values over the period with shift
    int highestIndex = iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, DonchianPeriod, barIndex + DonchianShift);
    int lowestIndex = iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, DonchianPeriod, barIndex + DonchianShift);
    
    double highestHigh = iHigh(_Symbol, PERIOD_CURRENT, highestIndex);
    double lowestLow = iLow(_Symbol, PERIOD_CURRENT, lowestIndex);
    
    // Calculate midline: Low + 0.5 * (High - Low)
    double midline = lowestLow + 0.5 * (highestHigh - lowestLow);
    
    return midline;
}

//+------------------------------------------------------------------+
//| Get RSI value for specified bar                                 |
//+------------------------------------------------------------------+
double GetRSIValue(int barIndex)
{
    double rsiBuffer[1];

    // Copy RSI value from indicator buffer
    if(CopyBuffer(rsiHandle, 0, barIndex, 1, rsiBuffer) <= 0)
    {
        Print("Failed to get RSI data for bar ", barIndex);
        return EMPTY_VALUE;
    }

    return rsiBuffer[0];
}

//+------------------------------------------------------------------+
//| Open buy trade                                                  |
//+------------------------------------------------------------------+
bool OpenBuyTrade()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double tp = ask + (TakeProfitPips * pipValue);

    if(trade.Buy(LotSize, _Symbol, ask, 0, tp, TradeComment))
    {
        Print("Buy order opened at ", ask, " TP: ", tp, " RSI: ", GetRSIValue(1));
        return true;
    }
    else
    {
        Print("Failed to open buy order. Error: ", GetLastError());
        return false;
    }
}

//+------------------------------------------------------------------+
//| Open sell trade                                                 |
//+------------------------------------------------------------------+
bool OpenSellTrade()
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double tp = bid - (TakeProfitPips * pipValue);

    if(trade.Sell(LotSize, _Symbol, bid, 0, tp, TradeComment))
    {
        Print("Sell order opened at ", bid, " TP: ", tp, " RSI: ", GetRSIValue(1));
        return true;
    }
    else
    {
        Print("Failed to open sell order. Error: ", GetLastError());
        return false;
    }
}

//+------------------------------------------------------------------+
//| Close opposite positions                                         |
//+------------------------------------------------------------------+
void CloseOppositePositions(ENUM_ORDER_TYPE oppositeType)
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetSymbol(i) == _Symbol)
        {
            ulong ticket = PositionGetTicket(i);
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

            // Close opposite position
            if((oppositeType == ORDER_TYPE_BUY && posType == POSITION_TYPE_SELL) ||
               (oppositeType == ORDER_TYPE_SELL && posType == POSITION_TYPE_BUY))
            {
                if(trade.PositionClose(ticket))
                {
                    Print("Closed opposite position #", ticket);
                    // Remove from tracking arrays
                    RemoveFromBreakevenArray(ticket);
                    RemoveFromPartialClosedArray(ticket);
                }
                else
                {
                    Print("Failed to close position #", ticket, ". Error: ", GetLastError());
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Manage trailing stops for all open positions                    |
//+------------------------------------------------------------------+
void ManageTrailingStops()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == _Symbol)
        {
            ulong ticket = PositionGetTicket(i);
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            double currentSL = PositionGetDouble(POSITION_SL);

            // Skip if already moved to breakeven
            if(IsInBreakevenArray(ticket))
                continue;

            double currentPrice;
            double breakevenSL;
            bool shouldMoveToBreakeven = false;

            if(posType == POSITION_TYPE_BUY)
            {
                currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
                breakevenSL = openPrice + (1.0 * pipValue);

                // Check if position is 1 pip in profit
                if(currentPrice >= openPrice + (5.0 * pipValue))
                    shouldMoveToBreakeven = true;
            }
            else if(posType == POSITION_TYPE_SELL)
            {
                currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
                breakevenSL = openPrice - (1.0 * pipValue);

                // Check if position is 1 pip in profit
                if(currentPrice <= openPrice - (5.0 * pipValue))
                    shouldMoveToBreakeven = true;
            }

            // Execute partial close and move stop loss to breakeven if conditions are met
            if(shouldMoveToBreakeven)
            {
                // First, attempt partial close of 50% of position
                bool partialCloseSuccess = PartialClosePosition(ticket);

                // Then move stop loss to breakeven
                if(trade.PositionModify(ticket, breakevenSL, PositionGetDouble(POSITION_TP)))
                {
                    Print("Position #", ticket, " stop loss moved to breakeven at ", breakevenSL);
                    AddToBreakevenArray(ticket);
                }
                else
                {
                    Print("Failed to modify position #", ticket, " stop loss. Error: ", GetLastError());
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Manage advanced trailing stops for positions with >10 pip TP   |
//+------------------------------------------------------------------+
void ManageAdvancedTrailing()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == _Symbol)
        {
            ulong ticket = PositionGetTicket(i);
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            double currentTP = PositionGetDouble(POSITION_TP);
            double currentSL = PositionGetDouble(POSITION_SL);

            // Check if position has TP > 10 pips
            double tpDistance = 0;
            if(posType == POSITION_TYPE_BUY && currentTP > 0)
                tpDistance = (currentTP - openPrice) / pipValue;
            else if(posType == POSITION_TYPE_SELL && currentTP > 0)
                tpDistance = (openPrice - currentTP) / pipValue;

            // Only apply advanced trailing if TP > 10 pips
            if(tpDistance <= 10.0)
                continue;

            double currentPrice;
            double profitPips = 0;

            if(posType == POSITION_TYPE_BUY)
            {
                currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
                profitPips = (currentPrice - openPrice) / pipValue;
            }
            else if(posType == POSITION_TYPE_SELL)
            {
                currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
                profitPips = (openPrice - currentPrice) / pipValue;
            }

            // Start trailing after 15 pips profit
            if(profitPips >= 15.0)
            {
                UpdateAdvancedTrailingStop(ticket, posType, openPrice, currentPrice, profitPips);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Update advanced trailing stop based on profit movement          |
//+------------------------------------------------------------------+
void UpdateAdvancedTrailingStop(ulong ticket, ENUM_POSITION_TYPE posType, double openPrice, double currentPrice, double profitPips)
{
    int trailingIndex = GetTrailingIndex(ticket);
    double highWaterPrice = 0;

    if(trailingIndex == -1)
    {
        // First time tracking this position
        AddToTrailingArray(ticket, currentPrice);
        trailingIndex = GetTrailingIndex(ticket);
        highWaterPrice = currentPrice;
    }
    else
    {
        highWaterPrice = trailingHighWater[trailingIndex];
    }

    bool shouldUpdateSL = false;
    double newSL = 0;

    if(posType == POSITION_TYPE_BUY)
    {
        // Update high water mark if price moved higher
        if(currentPrice > highWaterPrice)
        {
            trailingHighWater[trailingIndex] = currentPrice;
            highWaterPrice = currentPrice;
        }

        // Calculate trailing stop: 1 pip trail for each pip above 15 pip profit
        double trailDistance = MathMax(1.0, profitPips - 14.0); // Start at 1 pip trail at 15 pip profit
        newSL = highWaterPrice - (trailDistance * pipValue);

        // Only update if new SL is higher than current SL
        double currentSL = PositionGetDouble(POSITION_SL);
        if(newSL > currentSL)
            shouldUpdateSL = true;
    }
    else if(posType == POSITION_TYPE_SELL)
    {
        // Update low water mark if price moved lower
        if(currentPrice < highWaterPrice || highWaterPrice == 0)
        {
            trailingHighWater[trailingIndex] = currentPrice;
            highWaterPrice = currentPrice;
        }

        // Calculate trailing stop: 1 pip trail for each pip above 15 pip profit
        double trailDistance = MathMax(1.0, profitPips - 14.0); // Start at 1 pip trail at 15 pip profit
        newSL = highWaterPrice + (trailDistance * pipValue);

        // Only update if new SL is lower than current SL (or no SL set)
        double currentSL = PositionGetDouble(POSITION_SL);
        if(newSL < currentSL || currentSL == 0)
            shouldUpdateSL = true;
    }

    // Update stop loss if conditions are met
    if(shouldUpdateSL)
    {
        if(trade.PositionModify(ticket, newSL, PositionGetDouble(POSITION_TP)))
        {
            Print("Advanced trailing SL updated for position #", ticket, " to ", newSL,
                  " (Profit: ", DoubleToString(profitPips, 1), " pips)");
        }
        else
        {
            Print("Failed to update advanced trailing SL for position #", ticket, ". Error: ", GetLastError());
        }
    }
}

//+------------------------------------------------------------------+
//| Manage hedging for positions in drawdown                        |
//+------------------------------------------------------------------+
void ManageHedging()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == _Symbol)
        {
            ulong ticket = PositionGetTicket(i);
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);

            // Skip if already hedged
            if(IsInHedgedArray(ticket))
                continue;

            double currentPrice;
            bool shouldHedge = false;

            if(posType == POSITION_TYPE_BUY)
            {
                currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);

                // Check if position is 5 pips in loss
                if(currentPrice <= openPrice - (5.0 * pipValue))
                    shouldHedge = true;
            }
            else if(posType == POSITION_TYPE_SELL)
            {
                currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

                // Check if position is 5 pips in loss
                if(currentPrice >= openPrice + (5.0 * pipValue))
                    shouldHedge = true;
            }

            // Open hedge trade if conditions are met
            if(shouldHedge)
            {
                bool hedgeSuccess = OpenHedgeTrade(posType);
                if(hedgeSuccess)
                {
                    AddToHedgedArray(ticket);
                    Print("Hedge trade opened for position #", ticket, " due to 5 pip drawdown");
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Open hedge trade opposite to losing position                    |
//+------------------------------------------------------------------+
bool OpenHedgeTrade(ENUM_POSITION_TYPE originalType)
{
    if(originalType == POSITION_TYPE_BUY)
    {
        // Open sell hedge
        double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        double tp = bid - (TakeProfitPips * pipValue);

        if(trade.Sell(LotSize, _Symbol, bid, 0, tp, TradeComment + "_HEDGE"))
        {
            Print("Hedge sell order opened at ", bid, " TP: ", tp);
            return true;
        }
        else
        {
            Print("Failed to open hedge sell order. Error: ", GetLastError());
            return false;
        }
    }
    else if(originalType == POSITION_TYPE_SELL)
    {
        // Open buy hedge
        double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        double tp = ask + (TakeProfitPips * pipValue);

        if(trade.Buy(LotSize, _Symbol, ask, 0, tp, TradeComment + "_HEDGE"))
        {
            Print("Hedge buy order opened at ", ask, " TP: ", tp);
            return true;
        }
        else
        {
            Print("Failed to open hedge buy order. Error: ", GetLastError());
            return false;
        }
    }

    return false;
}

//+------------------------------------------------------------------+
//| Check if position is in breakeven array                         |
//+------------------------------------------------------------------+
bool IsInBreakevenArray(ulong ticket)
{
    int size = ArraySize(breakevenPositions);
    for(int i = 0; i < size; i++)
    {
        if(breakevenPositions[i] == ticket)
            return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Add position to breakeven array                                 |
//+------------------------------------------------------------------+
void AddToBreakevenArray(ulong ticket)
{
    int size = ArraySize(breakevenPositions);
    ArrayResize(breakevenPositions, size + 1);
    breakevenPositions[size] = ticket;
}

//+------------------------------------------------------------------+
//| Remove position from breakeven array                            |
//+------------------------------------------------------------------+
void RemoveFromBreakevenArray(ulong ticket)
{
    int size = ArraySize(breakevenPositions);
    for(int i = 0; i < size; i++)
    {
        if(breakevenPositions[i] == ticket)
        {
            // Shift remaining elements
            for(int j = i; j < size - 1; j++)
            {
                breakevenPositions[j] = breakevenPositions[j + 1];
            }
            ArrayResize(breakevenPositions, size - 1);
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| Partial close position (50% of volume)                          |
//+------------------------------------------------------------------+
bool PartialClosePosition(ulong ticket)
{
    // Check if position was already partially closed
    if(IsInPartialClosedArray(ticket))
        return true; // Already processed

    // Get position information
    if(!PositionSelectByTicket(ticket))
    {
        Print("Failed to select position #", ticket, " for partial close");
        return false;
    }

    double currentVolume = PositionGetDouble(POSITION_VOLUME);
    double closeVolume = NormalizeVolume(currentVolume * 0.5); // 50% of position

    // Ensure we have a valid close volume
    if(closeVolume <= 0)
    {
        Print("Invalid close volume calculated for position #", ticket);
        return false;
    }

    // Attempt partial close
    if(trade.PositionClosePartial(ticket, closeVolume))
    {
        Print("Partial close successful for position #", ticket,
              ". Closed volume: ", closeVolume, " of ", currentVolume);
        AddToPartialClosedArray(ticket);
        return true;
    }
    else
    {
        Print("Failed to partially close position #", ticket, ". Error: ", GetLastError());
        return false;
    }
}

//+------------------------------------------------------------------+
//| Normalize volume to broker's lot step                           |
//+------------------------------------------------------------------+
double NormalizeVolume(double volume)
{
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);

    if(lotStep > 0)
        volume = MathRound(volume / lotStep) * lotStep;

    if(volume < minLot)
        volume = minLot;
    if(volume > maxLot)
        volume = maxLot;

    return volume;
}

//+------------------------------------------------------------------+
//| Check if position is in partial closed array                    |
//+------------------------------------------------------------------+
bool IsInPartialClosedArray(ulong ticket)
{
    int size = ArraySize(partialClosedPositions);
    for(int i = 0; i < size; i++)
    {
        if(partialClosedPositions[i] == ticket)
            return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Add position to partial closed array                            |
//+------------------------------------------------------------------+
void AddToPartialClosedArray(ulong ticket)
{
    int size = ArraySize(partialClosedPositions);
    ArrayResize(partialClosedPositions, size + 1);
    partialClosedPositions[size] = ticket;
}

//+------------------------------------------------------------------+
//| Remove position from partial closed array                       |
//+------------------------------------------------------------------+
void RemoveFromPartialClosedArray(ulong ticket)
{
    int size = ArraySize(partialClosedPositions);
    for(int i = 0; i < size; i++)
    {
        if(partialClosedPositions[i] == ticket)
        {
            // Shift remaining elements
            for(int j = i; j < size - 1; j++)
            {
                partialClosedPositions[j] = partialClosedPositions[j + 1];
            }
            ArrayResize(partialClosedPositions, size - 1);
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| Close all open positions outside trading hours                  |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    static datetime lastCloseTime = 0;
    datetime currentTime = TimeCurrent();

    // Only attempt to close positions once per minute to avoid excessive attempts
    if(currentTime - lastCloseTime < 60)
        return;

    lastCloseTime = currentTime;

    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetSymbol(i) == _Symbol)
        {
            ulong ticket = PositionGetTicket(i);

            if(trade.PositionClose(ticket))
            {
                Print("Position #", ticket, " closed due to outside trading hours");
                // Remove from tracking arrays
                RemoveFromBreakevenArray(ticket);
                RemoveFromPartialClosedArray(ticket);
                RemoveFromHedgedArray(ticket);
                RemoveFromTrailingArray(ticket);
                RemoveFromTrailingArray(ticket);
                RemoveFromHedgedArray(ticket);
            }
            else
            {
                Print("Failed to close position #", ticket, " outside trading hours. Error: ", GetLastError());
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check if position is in hedged array                            |
//+------------------------------------------------------------------+
bool IsInHedgedArray(ulong ticket)
{
    int size = ArraySize(hedgedPositions);
    for(int i = 0; i < size; i++)
    {
        if(hedgedPositions[i] == ticket)
            return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Add position to hedged array                                    |
//+------------------------------------------------------------------+
void AddToHedgedArray(ulong ticket)
{
    int size = ArraySize(hedgedPositions);
    ArrayResize(hedgedPositions, size + 1);
    hedgedPositions[size] = ticket;
}

//+------------------------------------------------------------------+
//| Remove position from hedged array                               |
//+------------------------------------------------------------------+
void RemoveFromHedgedArray(ulong ticket)
{
    int size = ArraySize(hedgedPositions);
    for(int i = 0; i < size; i++)
    {
        if(hedgedPositions[i] == ticket)
        {
            // Shift remaining elements
            for(int j = i; j < size - 1; j++)
            {
                hedgedPositions[j] = hedgedPositions[j + 1];
            }
            ArrayResize(hedgedPositions, size - 1);
            break;
        }
    }
}

//+------------------------------------------------------------------+
//| Get index of position in trailing array                         |
//+------------------------------------------------------------------+
int GetTrailingIndex(ulong ticket)
{
    int size = ArraySize(trailingPositions);
    for(int i = 0; i < size; i++)
    {
        if(trailingPositions[i] == ticket)
            return i;
    }
    return -1;
}

//+------------------------------------------------------------------+
//| Add position to trailing array with high water price            |
//+------------------------------------------------------------------+
void AddToTrailingArray(ulong ticket, double highWaterPrice)
{
    int size = ArraySize(trailingPositions);
    ArrayResize(trailingPositions, size + 1);
    ArrayResize(trailingHighWater, size + 1);
    trailingPositions[size] = ticket;
    trailingHighWater[size] = highWaterPrice;
}

//+------------------------------------------------------------------+
//| Remove position from trailing array                             |
//+------------------------------------------------------------------+
void RemoveFromTrailingArray(ulong ticket)
{
    int size = ArraySize(trailingPositions);
    for(int i = 0; i < size; i++)
    {
        if(trailingPositions[i] == ticket)
        {
            // Shift remaining elements
            for(int j = i; j < size - 1; j++)
            {
                trailingPositions[j] = trailingPositions[j + 1];
                trailingHighWater[j] = trailingHighWater[j + 1];
            }
            ArrayResize(trailingPositions, size - 1);
            ArrayResize(trailingHighWater, size - 1);
            break;
        }
    }
}
