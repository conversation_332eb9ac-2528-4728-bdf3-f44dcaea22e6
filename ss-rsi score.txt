//@version=6
indicator("SS Score & RSI Score Oscillator", shorttitle="SSRSI Osc", overlay=false)

// --- Input Parameters ---
smoothness  = input.float(0.33, "ATR Smoothness Factor", step=0.01)
atrLength   = input.int(21, "ATR Length", minval=1)
rsiLength   = input.int(14, "RSI Length", minval=1)
// RSI thresholds (from Python logic)
rsiHighThresh    = input.float(70.0, "RSI High Threshold")
rsiMidHighThresh = input.float(55.9, "RSI Upper-Mid Threshold")
rsiMidLowThresh  = input.float(44.1, "RSI Lower-Mid Threshold")
rsiLowThresh     = input.float(30.0, "RSI Low Threshold")

// --- Calculations: ATR and Modified RSI ---
tr  = math.max(high - low, math.abs(high - close[1]), math.abs(low - close[1]))
atr = ta.sma(tr, atrLength)

delta    = close - close[1]
gain     = (delta > 0 and delta > smoothness * atr and delta > nz(delta[1], 0)) ? delta : 0
loss     = (delta < 0 and math.abs(delta) > smoothness * atr and math.abs(delta) > math.abs(nz(delta[1], 0))) ? math.abs(delta) : 0
avg_gain = ta.sma(gain, rsiLength)
avg_loss = ta.sma(loss, rsiLength)
rs  = avg_loss != 0 ? avg_gain / avg_loss : 0
rsi = avg_loss == 0 ? (avg_gain == 0 ? 50 : 100) : (100 - 100 / (1 + rs))

// --- Determine Bar State and Trend for SS Score ---
// Here we define a result category:
// 3 = "HHLL" (both higher high and lower low)
// 2 = "HH"  (higher high only)
// 1 = "LL"  (lower low only)
// 0 = "NA"  (neither)
var int resultVal = 0
if bar_index > 0
    bool newHigh = high > high[1]
    bool newLow  = low < low[1]
    if newHigh and newLow
        resultVal := 3
    else if newHigh
        resultVal := 2
    else if newLow
        resultVal := 1
    else
        resultVal := 0
else
    resultVal := 0

// Determine trend order: 1 for "L->H" (upward swing) and -1 for "H->L" (downward swing)
var int orderVal = 0
if bar_index == 0
    orderVal := 0
else if bar_index == 1
    orderVal := resultVal == 2 ? 1 : (resultVal == 1 ? -1 : (close >= close[1] ? 1 : -1))
else
    orderVal := nz(orderVal[1])
    if resultVal == 2 and resultVal != 1
        orderVal := 1
    else if resultVal == 1 and resultVal != 2
        orderVal := -1

// Define state value based on order, direction, and result
// For an upward trend (orderVal == 1):
//    if bar is up and result is HH, state = 8; else state = 7
// For a downward trend (orderVal == -1):
//    if bar is down and result is LL, state = 1; else state = 2
// Additionally, when upward trend but bar is down: state 6 or 5; downward trend but bar is up: state 3 or 4.
var int stateVal = 0
bool dirUp = close >= close[1]
if orderVal == 1
    stateVal := dirUp ? (resultVal == 2 ? 8 : 7) : (resultVal == 2 ? 6 : 5)
else if orderVal == -1
    stateVal := dirUp ? (resultVal == 1 ? 3 : 4) : (resultVal == 1 ? 1 : 2)
else
    stateVal := 0

// --- Compute Cumulative SS Score using if-else chain ---
var float ssScore = 0.0
if bar_index == 0
    ssScore := 0
else
    float prevSS = nz(ssScore[1], 0)
    if stateVal == 8
        ssScore := prevSS + 3
    else if stateVal == 7
        ssScore := prevSS + 2
    else if stateVal == 6
        ssScore := prevSS + 1
    else if stateVal == 3
        ssScore := prevSS - 1
    else if stateVal == 2
        ssScore := prevSS - 2
    else if stateVal == 1
        ssScore := prevSS - 3
    else
        ssScore := prevSS

// --- Compute Cumulative RSI Score ---
var float rsiScore = 0.0
if bar_index == 0
    rsiScore := 0
else
    float prevRS = nz(rsiScore[1], 0)
    if rsi > rsiHighThresh
        rsiScore := prevRS + 2
    else if rsi > rsiMidHighThresh
        rsiScore := prevRS + 1
    else if rsi >= rsiMidLowThresh and rsi <= rsiMidHighThresh
        rsiScore := 0
    else if rsi < rsiLowThresh
        rsiScore := prevRS - 2
    else
        rsiScore := prevRS - 1

// --- Plot the SS score and RSI score as two lines ---
plot(ssScore, color=ssScore>ssScore[1] ? color.blue : ssScore<ssScore[1] ? color.red : color.yellow,   title="SS Score")
plot(rsiScore, color=color.orange, title="RSI Score")
