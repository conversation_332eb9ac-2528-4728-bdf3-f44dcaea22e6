//@version=5
indicator("Special RSI", overlay=false)

smoothness = input.float(title = 'Smoothness', defval=1)
showma = input.bool(title = 'Show MA', defval=false)

atr_val = ta.atr(21)[14]

// Set the length for RSI
length = 14

// Calculate price change
change = close - close[1]

// Separate gains and losses
gain = change > 0 and change > smoothness * atr_val and change > change[1] ? change : 0
loss = change < 0 and -change > smoothness * atr_val and -change > -change[1] ? math.abs(change) : 0

// Calculate smoothed average gains and losses using <PERSON>'s smoothing method. 
// <PERSON>'s smoothing: avgGain = (prev_avgGain * (length - 1) + currentGain) / length
// We can approximate that with ta.rma for a series.
avgGain = ta.rma(gain, length)
avgLoss = ta.rma(loss, length)

// Calculate Relative Strength (RS) and then RSI
rs = avgLoss == 0 ? na : avgGain / avgLoss
rsi = na(rs) ? 100 : 100 - (100 / (1 + rs))

sma1 = ta.sma(rsi, 14)

plot(rsi, color = rsi > 70 ? color.blue : rsi < 30 ? color.red : color.yellow, linewidth = 2, title="Special RSI")
plot(showma ? sma1 : na, color=color.red, title="SMA1")

rsiUpperBand = hline(70, "RSI Upper Band", color=#787B86)
midline = hline(50, "RSI Middle Band", color=color.new(#787B86, 50))
rsiLowerBand = hline(30, "RSI Lower Band", color=#787B86)