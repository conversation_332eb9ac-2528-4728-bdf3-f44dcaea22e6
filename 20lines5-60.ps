// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © sakisf

//@version=5
indicator("20s", overlay=true)


ema_m5 = request.security(syminfo.tickerid, "5", ta.ema(close, 20))
ema_h1 = request.security(syminfo.tickerid, "60", ta.ema(close, 20))

// Plot the EMA ribbons
p5 = plot(ema_m5, title="M5 EMA20", color=color.new(color.blue, 100), linewidth=2)
p11 = plot(ema_h1, title="H1 EMA20", color=color.new(color.red, 100), linewidth=2)
fill(p5, p11, color=color.new(color.orange, 90))