//@version=5
indicator("Currency Pair Volatility and Correlation", overlay=true)

// Function to calculate volatility
volatility(src, length) =>
    ta.stdev(src, length)

// Function to calculate correlation
correlation(src1, src2, length) =>
    ta.correlation(src1, src2, length)

// List of currency pairs
currencyPairs = array.new_string(28)
array.push(currencyPairs, "EURGBP")
array.push(currencyPairs, "EURAUD")
array.push(currencyPairs, "EURNZD")
array.push(currencyPairs, "EURUSD")
array.push(currencyPairs, "EURCAD")
array.push(currencyPairs, "EURCHF")
array.push(currencyPairs, "EURJPY")
array.push(currencyPairs, "GBPUSD")
array.push(currencyPairs, "GBPAUD")
array.push(currencyPairs, "GBPNZD")
array.push(currencyPairs, "GBPCAD")
array.push(currencyPairs, "GBPCHF")
array.push(currencyPairs, "GBPJPY")
array.push(currencyPairs, "AUDUSD")
array.push(currencyPairs, "AUDNZD")
array.push(currencyPairs, "AUDCAD")
array.push(currencyPairs, "AUDCHF")
array.push(currencyPairs, "AUDJPY")
array.push(currencyPairs, "NZDUSD")
array.push(currencyPairs, "NZDCAD")
array.push(currencyPairs, "NZDCHF")
array.push(currencyPairs, "NZDJPY")
array.push(currencyPairs, "USDCAD")
array.push(currencyPairs, "USDCHF")
array.push(currencyPairs, "USDJPY")
array.push(currencyPairs, "CADCHF")
array.push(currencyPairs, "CADJPY")
array.push(currencyPairs, "CHFJPY")

// Main pairs for correlation analysis
mainPairs = array.new_string(7)
array.push(mainPairs, "EURUSD")
array.push(mainPairs, "GBPUSD")
array.push(mainPairs, "AUDUSD")
array.push(mainPairs, "NZDUSD")
array.push(mainPairs, "USDCAD")
array.push(mainPairs, "USDCHF")
array.push(mainPairs, "USDJPY")

// Length for volatility and correlation calculation
length = 21

// Calculate volatility and correlation for each pair
volatilities = array.new_float()
correlations = array.new_float()

for i = 0 to array.size(currencyPairs) - 1
    pair = array.get(currencyPairs, i)
    pairClose = request.security(pair, "D", close)
    vol = volatility(pairClose, length)
    array.push(volatilities, vol)
    
    var string mainPair = na
    for j = 0 to array.size(mainPairs) - 1
        mp = array.get(mainPairs, j)
        if str.contains(pair, str.left(mp, 3))
            mainPair := mp
            break
    if not na(mainPair)
        mainPairClose = request.security(mainPair, "D", close)
        corr = correlation(pairClose, mainPairClose, length)
        array.push(correlations, corr)

// Plot quadrant chart
var table quadrantTable = table.new(position.top_right, 1, 1, border_width=1)
table.cell(quadrantTable, 0, 0, "Quadrant Chart", bgcolor=color.gray)

for i = 0 to array.size(volatilities) - 1
    vol = array.get(volatilities, i)
    corr = array.get(correlations, i)
    
    quadrant = na
    if corr < 0 and vol > 0.01
        quadrant := "Negative correlation, high volatility"
    else if corr > 0 and vol > 0.01
        quadrant := "Positive correlation, high volatility"
    else if corr < 0 and vol <= 0.01
        quadrant := "Negative correlation, low volatility"
    else if corr > 0 and vol <= 0.01
        quadrant := "Positive correlation, low volatility"
    
    label.new(bar_index, high, quadrant, color=color.white, textcolor=color.black, size=size.small, style=label.style_label_down)