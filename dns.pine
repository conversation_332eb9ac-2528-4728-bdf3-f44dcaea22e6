//@version=5
indicator("DEEZNUTS • smoothed", shorttitle="DN-S", overlay = true)

// ─────── Inputs
fastLen   = input.int(1 , "Fast MA length",  minval = 1)
slowLenIn = input.int(5 , "Slow MA length",  minval = 1)          // plain constant bound
smoothLen = input.int(3 , "Oscillator smoothing", minval = 1,
                      tooltip = "Set to 1 to disable extra smoothing")
maType    = input.string("EMA", "MA type",
              options = ["SMA", "EMA", "RMA", "WMA"])

// Make sure slowLen > fastLen (otherwise it auto-bumps)
slowLen   = math.max(slowLenIn, fastLen + 1)

// Optional notice in Data Window if an auto-bump happened
plotchar(slowLenIn < slowLen ? 1 : na, title = "Auto-bump flag",
         char='⚠', location = location.top, size = size.tiny, color = color.orange)

// ─────── MA helper
f_ma(src, len) =>
    switch maType
        "EMA" => ta.ema(src, len)
        "RMA" => ta.rma(src, len)
        "WMA" => ta.wma(src, len)
        =>        ta.sma(src, len)

// ─────── Core calculation
fast  = f_ma(close, fastLen)
slow  = f_ma(close, slowLen)
raw   = fast - slow
osc   = smoothLen > 1 ? ta.rma(raw, smoothLen) : raw   // extra smoothing

// ─────── Plot
col   = osc >= 0 ? color.teal : color.red
//plot(osc, color = col, linewidth = 2, title = "DEEZNUTS")
//hline(0, "Zero", color = color.gray, linestyle = hline.style_dotted)

// ────────── Horizontal line at +1 with bar-open logic
// Value of the oscillator at this bar’s open is the previous bar’s close value.
oscOpen   = nz(osc[1])
oneCol    = oscOpen > 0 ? color.blue : color.red
//plot(0,   title="0 line", color=oneCol, linewidth=2)


// ────────── ★ Candle painting ★
//barcolor(osc > 0 ? color.rgb(0, 0, 255) : color.rgb(255, 0, 0), title = "Candle colour")
//bgcolor(osc > 0 ? color.blue : color.red, title = "Candle colour")

// ─── Candle colours based on *current* oscillator value ────────────────────────
candleCol = osc > 0 ? color.blue : color.red
barcolor(color.new(color.black, 100))
// Plot replacement candles on the price chart
plotcandle(open, high, low, close,
           title       = "DEEZNUTS candles",
           color       = candleCol,
           wickcolor   = candleCol,
           bordercolor = candleCol
           )