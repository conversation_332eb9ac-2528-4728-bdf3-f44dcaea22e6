// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © sakisf

//@version=5
indicator('SakisMAs', shorttitle='BSBig', overlay=true)

ma1 = input(title='1st EMA Period', defval=10)
//plot(ta.sma(close, ma1), title='1st EMA', color=color.aqua, linewidth=1)
//plot((ta.ema(close, 50) + ta.ema(close, 100)) / 2, title='1st EMA (combo/2)', color=color.new(#180084, 0), linewidth=2)
ma2 = input(title='2nd EMA Period', defval=34)
//plot(ta.sma(close, ma2), title='2nd EMA', color=color.new(#ffeb3a, 0), linewidth=2)
ma21 = input(title='21nd EMA Period', defval=13)
//plot(ta.ema(close, ma21), title='2nd EMA', color=color.new(#27dfff, 0), linewidth=1)
ma3 = input(title='3rd EMA Period a (combo/2)', defval=50)
//plot(ema(close, ma3), title = "3rd EMA", color = #00bcd4, linewidth = 1)
ma4 = input(title='3th EMA Period b (combo/2)', defval=100)
//plot((ta.ema(close, ma3) + ta.ema(close, ma4)) / 2, title='3rd EMA (combo/2)', color=color.new(#180084, 0), linewidth=2)
ma5 = input(title='4th EMA Period c', defval=200)
//#FF0000
plot(ta.ema(close, ma5), title='4th EMA c', color=#ff0000, linewidth=1)// trackprice=true)
ma6 = input(title='5th EMA Period c', defval=200)
//plot(ta.ema(high, ma6), title='5th EMA c', color=color.rgb(255, 0, 0), linewidth=2)
ma61 = input(title='61th EMA Period c', defval=300)
//plot(ta.ema(close, ma61), title='61th EMA c', color=color.new(color.aqua, 0), linewidth=2)
ma7 = input(title='6th EMA Period c', defval=200)
//#000000
//plot(ta.ema(low, ma7), title='6th EMA c', color=color.new(#FF0000, 0), linewidth=2)
ma8 = input(title='7th SMA Period', defval=200)
//plot(ta.sma(close, ma8), title='7th EMA', color=color.new(#4caf50, 0), linewidth=1)
ma9 = input(title='1st Ribbon EMA', defval=666)
ma10 = input(title='2nd Ribbon SMA', defval=666)
//p1 = plot(ta.ema(close, ma9), title='Ribbon EMA', color=color.new(#ff9800, 50), linewidth=1)
//p2 = plot(ta.sma(close, ma10), title='Ribbon SMA', color=color.new(#ffeb3b, 50), linewidth=1, trackprice=true)
//fill(p1, p2, color=ma8 > ma9 ? #ff9800 : #ffeb3b, transp=60)

//ma11 = input(title = "8th EMA Period", type = input.integer, defval = 205)
//plot(ema(hl2, ma11), title = "8th EMA c", color = #521425, linewidth = 2)
//ma12 = input(title = "9th SMA Period", type = input.integer, defval = 255)
//plot(ema(hl2, ma12), title = "9th EMA c", color = #521425, linewidth = 2)

//ma14 = input(title = "10th EMA Period (hlc3)", type = input.integer, defval = 10)
//plot(ema(close, ma14), title = "10th EMA", color = color.aqua, linewidth = 1)

ma13 = input(title='TEMA Period', defval=180)
TEMA(series, length) =>
    if length > 0
        ema1 = ta.ema(series, ma13)
        ema2 = ta.ema(ema1, ma13)
        ema3 = ta.ema(ema2, ma13)
        3 * ema1 - 3 * ema2 + ema3
    else
        na
plot(TEMA(close, ma13), title = "TEMA c", color = #ff8fad, linewidth = 1)

anchor = input.string(defval='Session', title='Anchor Period', options=['Session', 'Week', 'Month', 'Year'])

MILLIS_IN_DAY = 86400000

dwmBarTime = timeframe.isdwm ? time : time('D')
// If it's a short day, then there could be no daily bar. Take a previous one.
if na(dwmBarTime)
    dwmBarTime := nz(dwmBarTime[1])
    dwmBarTime
var periodStart = time - time  // zero

// in pine week starts on Sunday and it's value 1. Value of Monday is 2
// we need a week to start on Monday and its value should be 0
makeMondayZero(dayOfWeek) =>
    (dayOfWeek + 5) % 7

isMidnight(t) =>
    hour(t) == 0 and minute(t) == 0

isSameDay(t1, t2) =>
    dayofmonth(t1) == dayofmonth(t2) and month(t1) == month(t2) and year(t1) == year(t2)

isOvernight() =>
    not(isMidnight(dwmBarTime) or request.security(syminfo.tickerid, 'D', isSameDay(time, time_close), lookahead=barmerge.lookahead_on))

tradingDayStart(t) =>
    y = year(t)
    m = month(t)
    d = dayofmonth(t)
    timestamp(y, m, d, 0, 0)

numDaysBetween(time1, time2) =>
    y1 = year(time1)
    m1 = month(time1)
    d1 = dayofmonth(time1)

    y2 = year(time2)
    m2 = month(time2)
    d2 = dayofmonth(time2)

    diff = math.abs(timestamp('GMT', y1, m1, d1, 0, 0) - timestamp('GMT', y2, m2, d2, 0, 0))
    diff / MILLIS_IN_DAY

// a symbol with overnight session starts at previos day
// to get the trading day we should get the next day after the session's start
tradingDay = isOvernight() ? tradingDayStart(dwmBarTime + MILLIS_IN_DAY) : tradingDayStart(dwmBarTime)

isNewPeriod() =>
    isNew = false
    if tradingDay != nz(tradingDay[1])
        if anchor == 'Session'
            isNew := na(tradingDay[1]) or tradingDay > tradingDay[1]
            isNew

        if anchor == 'Week'
            DAYS_IN_WEEK = 7
            isNew := makeMondayZero(dayofweek(periodStart)) + numDaysBetween(periodStart, tradingDay) >= DAYS_IN_WEEK
            isNew

        if anchor == 'Month'
            isNew := month(periodStart) != month(tradingDay) or year(periodStart) != year(tradingDay)
            isNew

        if anchor == 'Year'
            isNew := year(periodStart) != year(tradingDay)
            isNew
    isNew

src = hlc3
sumSrc = float(na)
sumVol = float(na)

sumSrc := nz(sumSrc[1], 0)
sumVol := nz(sumVol[1], 0)

if isNewPeriod()
    periodStart := tradingDay
    sumSrc := 0.0
    sumVol := 0.0
    sumVol


if not na(src) and not na(volume)
    sumSrc += src * volume
    sumVol += volume
    sumVol

vwapValue = sumSrc / sumVol
//plot(vwapValue, title='VWAP', color=color.new(#FF6D00, 0), linewidth=2, trackprice=true)

//
//avg = src - vwapValue
//dev = sqrt(volume/sumVol - avg*avg)

//plot(vwapValue + dev, title="VWAP SDP", color=#FF6D00, linewidth=1)
//plot(vwapValue, title="VWAP_Line", color=#FF6D00, linewidth=1, show_last=1, trackprice = true)

