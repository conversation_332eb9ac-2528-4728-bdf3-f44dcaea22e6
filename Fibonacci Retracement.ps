//@version=6
indicator("Fibonacci Retracement", overlay=true, max_bars_back = 250)

// Define the high and low points on a 1-hour timeframe
higherHigh224 = request.security(syminfo.tickerid, "60", ta.highest(high, 224)[25])
lowerLow224 = request.security(syminfo.tickerid, "60", ta.lowest(low, 224)[25])

// Calculate Fibonacci levels
fibLevels = array.new_float(27)
array.set(fibLevels, 0, lowerLow224)
array.set(fibLevels, 1, lowerLow224 + 0.059 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 2, lowerLow224 + 0.191 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 3, lowerLow224 + 0.25 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 4, lowerLow224 + 0.309 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 5, lowerLow224 + 0.441 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 6, lowerLow224 + 0.50 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 7, lowerLow224 + 0.559 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 8, lowerLow224 + 0.691 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 9, lowerLow224 + 0.75 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 10, lowerLow224 + 0.809 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 11, lowerLow224 + 0.941 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 12, higherHigh224)
array.set(fibLevels, 13, lowerLow224 + 1.059 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 14, lowerLow224 + 1.191 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 15, lowerLow224 + 1.25 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 16, lowerLow224 + 1.309 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 17, lowerLow224 + 1.441 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 18, lowerLow224 + 1.50 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 19, lowerLow224 + 1.559 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 20, lowerLow224 - 0.059 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 21, lowerLow224 - 0.191 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 22, lowerLow224 - 0.25 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 23, lowerLow224 - 0.309 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 24, lowerLow224 - 0.441 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 25, lowerLow224 - 0.50 * (higherHigh224 - lowerLow224))
array.set(fibLevels, 26, lowerLow224 - 0.559 * (higherHigh224 - lowerLow224))

// Draw Fibonacci retracement lines
// Optionally, add labels to the Fibonacci levels
f_updateLineAndLabel(index, lineColor, int xtra, color labelColor, string labelText, int xtral) =>
    var line lineVar = na
    var label labelVar = na

    if (not na(lineVar))
        line.delete(lineVar)
    lineVar := line.new(x1=bar_index - 224, y1=array.get(fibLevels, index), x2=bar_index, y2=array.get(fibLevels, index), color=lineColor, width=1)
    if (not na(xtra))
        lineVar.set_x2(bar_index + xtra)
    if (not na(labelVar))
        label.delete(labelVar)
    labelVar := label.new(x=bar_index + 5, y=array.get(fibLevels, index), text=labelText + " (" + str.tostring(array.get(fibLevels, index), format.mintick) + ")", color=labelColor, textcolor=color.blue, style=label.style_label_left, size=size.small)
    if (not na(xtral))
        labelVar.set_x(bar_index + xtral)
    if (na(labelText))
        label.delete(labelVar)
    [lineVar, labelVar]

[myLine0, myLabel0] = f_updateLineAndLabel(0, color.new(#666666, 68), 5, color.new(#666666, 68), "0 ", na)
[myLine1, mylabel1] = f_updateLineAndLabel(1, color.new(#666666, 68), na, na, na, na)
[myLine2, mylabel2] = f_updateLineAndLabel(2, color.new(#666666, 68), na, na, na, na)
[myLine3, mylabel3] = f_updateLineAndLabel(3, color.new(#666666, 68), 5, color.new(#666666, 68), "25 ", na)
[myLine4, mylabel4] = f_updateLineAndLabel(4, color.new(#666666, 68), na, na, na, na)
[myLine5, mylabel5] = f_updateLineAndLabel(5, color.new(#666666, 68), 10, color.new(#666666, 68), "m", 10)
[myLine6, myLabel6] = f_updateLineAndLabel(6, color.new(#666666, 68), 5, color.new(#666666, 68), "50 ", na)
[myLine7, myLabel7] = f_updateLineAndLabel(7, color.new(#666666, 68), 10, color.new(#666666, 68), "m", 10)
[myLine8, myLabel8] = f_updateLineAndLabel(8, color.new(#666666, 68), na, na, na, na)
[myLine9, myLabel9] = f_updateLineAndLabel(9, color.new(#666666, 68), 5, color.new(#666666, 68), "75 ", na)
[myLine10, myLabel10] = f_updateLineAndLabel(10, color.new(#666666, 68), na, na, na, na)
[myLine11, myLabel11] = f_updateLineAndLabel(11, color.new(#666666, 68), na, na, na, na)
[myLine12, myLabel12] = f_updateLineAndLabel(12, color.new(#666666, 68), 5, color.new(#666666, 68), "100 ", na)
[myLine13, myLabel13] = f_updateLineAndLabel(13, color.new(#666666, 68), na, na, na, na)
[myLine14, myLabel14] = f_updateLineAndLabel(14, color.new(#666666, 68), na, na, na, na)
[myLine15, myLabel15] = f_updateLineAndLabel(15, color.new(#666666, 68), 5, color.new(#666666, 68), "125 ", na)
[myLine16, myLabel16] = f_updateLineAndLabel(16, color.new(#666666, 68), na, na, na, na)
[myLine17, myLabel17] = f_updateLineAndLabel(17, color.new(#666666, 68), na, na, na, na)
[myLine18, myLabel18] = f_updateLineAndLabel(18, color.new(#666666, 68), 5, color.new(#666666, 68), "150 ", na)
[myLine19, myLabel19] = f_updateLineAndLabel(19, color.new(#666666, 68), na, na, na, na)
[myLine20, myLabel20] = f_updateLineAndLabel(20, color.new(#666666, 68), na, na, na, na)
[myLine21, myLabel21] = f_updateLineAndLabel(21, color.new(#666666, 68), na, na, na, na)
[myLine22, myLabel22] = f_updateLineAndLabel(22, color.new(#666666, 68), 5, color.new(#666666, 68), "-125 ", na)
[myLine23, myLabel23] = f_updateLineAndLabel(23, color.new(#666666, 68), na, na, na, na)
[myLine24, myLabel24] = f_updateLineAndLabel(24, color.new(#666666, 68), na, na, na, na)
[myLine25, myLabel25] = f_updateLineAndLabel(25, color.new(#666666, 68), 5, color.new(#666666, 68), "-150 ", na)
[myLine26, myLabel26] = f_updateLineAndLabel(26, color.new(#666666, 68), na, na, na, na)