//+------------------------------------------------------------------+
//|  DEEZNUTS_ColorBars.mq5                                          |
//|  MQL5 version with color bars                                    |
//|  Green bars when (FastMA-SlowMA) > 0, Red bars when ≤ 0         |
//+------------------------------------------------------------------+
#property copyright "Converted to MQL5"
#property link      ""
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 5
#property indicator_plots   1

//--- plot ColorBars
#property indicator_label1  "DEEZNUTS"
#property indicator_type1   DRAW_COLOR_CANDLES
#property indicator_color1  clrGreen,clrRed
#property indicator_style1  STYLE_SOLID
#property indicator_width1  1

//─── user inputs ────────────────────────────────────────────────────
input int FastLength   = 1;     // Fast MA period (≥1)
input int SlowLength   = 5;     // Slow MA period (> FastLength)
input int SmoothLength = 3;     // Wilder smoothing of (fast-slow); 1 = off
input ENUM_MA_METHOD MAMethod = MODE_EMA;  // MA Method

//─── indicator buffers ─────────────────────────────────────────────
double OpenBuffer[];
double HighBuffer[];
double LowBuffer[];
double CloseBuffer[];
double ColorBuffer[];

//─── work arrays ───────────────────────────────────────────────────
double rawOsc[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- indicator buffers mapping
   SetIndexBuffer(0, OpenBuffer);
   SetIndexBuffer(1, HighBuffer);
   SetIndexBuffer(2, LowBuffer);
   SetIndexBuffer(3, CloseBuffer);
   SetIndexBuffer(4, ColorBuffer, INDICATOR_COLOR_INDEX);
   
   //--- set drawing style
   PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_COLOR_CANDLES);
   PlotIndexSetInteger(0, PLOT_COLOR_INDEXES, 2);
   PlotIndexSetInteger(0, PLOT_LINE_COLOR, 0, clrGreen);
   PlotIndexSetInteger(0, PLOT_LINE_COLOR, 1, clrRed);
   
   //--- allocate work array
   ArrayResize(rawOsc, 1000);
   ArraySetAsSeries(rawOsc, true);
   
   //--- set arrays as series
   ArraySetAsSeries(OpenBuffer, true);
   ArraySetAsSeries(HighBuffer, true);
   ArraySetAsSeries(LowBuffer, true);
   ArraySetAsSeries(CloseBuffer, true);
   ArraySetAsSeries(ColorBuffer, true);
   
   //--- set indicator name
   string short_name = StringFormat("DEEZNUTS ColorBars (%d,%d,%d)", 
                                   FastLength, SlowLength, SmoothLength);
   IndicatorSetString(INDICATOR_SHORTNAME, short_name);
   
   //--- set digits
   IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   //--- check for minimum bars
   if(rates_total < SlowLength + SmoothLength + 5)
      return(0);
   
   //--- set arrays as series
   ArraySetAsSeries(open, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(close, true);
   
   //--- determine starting position
   int start = (prev_calculated == 0) ? 0 : prev_calculated - 1;
   
   //--- resize work array if needed
   if(ArraySize(rawOsc) < rates_total)
   {
      ArrayResize(rawOsc, rates_total);
      ArraySetAsSeries(rawOsc, true);
   }
   
   //--- main calculation loop
   for(int i = start; i < rates_total; i++)
   {
      int pos = rates_total - 1 - i;  // Convert to series index
      
      //── 1. Calculate raw oscillator using iMA
      double fast = iMA(NULL, 0, FastLength, 0, MAMethod, PRICE_OPEN, pos);
      double slow = iMA(NULL, 0, SlowLength, 0, MAMethod, PRICE_OPEN, pos);
      
      if(fast == 0.0 || slow == 0.0) continue; // Skip if MA not ready
      
      rawOsc[pos] = fast - slow;
      
      //── 2. Optional Wilder smoothing (SMMA)
      double osc;
      if(SmoothLength <= 1)
      {
         osc = rawOsc[pos];
      }
      else
      {
         // Calculate simple moving average for smoothing
         if(pos < SmoothLength - 1)
         {
            osc = rawOsc[pos]; // Not enough data for smoothing
         }
         else
         {
            double sum = 0;
            for(int j = 0; j < SmoothLength; j++)
               sum += rawOsc[pos + j];
            osc = sum / SmoothLength;
         }
      }
      
      //── 3. Set candle data and color based on oscillator
      OpenBuffer[pos] = open[pos];
      HighBuffer[pos] = high[pos];
      LowBuffer[pos] = low[pos];
      CloseBuffer[pos] = close[pos];
      
      // Color logic: Green (0) when osc > 0, Red (1) when osc <= 0
      if(osc > 0.0)
         ColorBuffer[pos] = 0;  // Green
      else
         ColorBuffer[pos] = 1;  // Red
   }
   
   //--- return value of prev_calculated for next call
   return(rates_total);
}
//+------------------------------------------------------------------+
