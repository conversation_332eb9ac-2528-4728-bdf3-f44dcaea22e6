//@version=6
indicator("Custom Linear Regression with ATR Bands", overlay = true, max_bars_back = 384, behind_chart = true)

// Parameters
price = input.source(close, title="Source")
length = input.int(288, minval=1, title="Period_Linear_predictor")
multiplier = input.float(2, title="RSS MULTIPLE")
atrLength = 21
lwmaLength = 10

// Linear Regression
lr = ta.linreg(price, length, 0)

// Calculate RSS
rss = math.sqrt(math.sum(math.pow(price - lr, 2), length) / (length - 1))
ratio = (price - lr) / rss

lprice = ta.lowest(price, length)
hprice = ta.highest(price, length)
lratio = ta.lowest(ratio, length)
hratio = ta.highest(ratio, length)

entry_long = price - lr <= -multiplier * rss and price == lprice and ratio > lratio
entry_short = price - lr >= multiplier * rss and price == hprice and ratio < hratio
plotshape(entry_long, title='OS', location=location.belowbar, color=color.new(color.white, 0), style=shape.triangleup, size=size.tiny)
plotshape(entry_short, title='OB', location=location.abovebar, color=color.new(color.white, 0), style=shape.triangledown, size=size.tiny)

rsinew = ta.rsi(price, 14)

var int elong = na
if (rsinew > 44.1 and rsinew < 55.9)
    elong := 1
else if (rsinew < 20)
    elong := 4
else if (rsinew > 80)
    elong := 5
else if (rsinew >= 20 and rsinew <= 30)
    elong := 2
else if (rsinew >= 70 and rsinew <= 80)
    elong := 3
else 
    elong:= na

plotshape(elong == 3, title = 'el', location=location.abovebar, color=color.new(color.white, 0), style=shape.xcross, size=size.auto)
plotshape(elong == 2, title = 'el', location=location.belowbar, color=color.new(color.white, 0), style=shape.xcross, size=size.auto)
plotshape(elong == 5, title = 'el', location=location.abovebar, color=color.new(color.red, 0), style=shape.xcross, size=size.tiny)
plotshape(elong == 4, title = 'el', location=location.belowbar, color=color.new(color.blue, 0), style=shape.xcross, size=size.tiny)

plotshape(elong == 1, title = 'el', location=location.belowbar, color=color.new(color.yellow, 0), style=shape.xcross, size=size.auto)
plotshape(elong == 1, title = 'el', location=location.abovebar, color=color.new(color.yellow, 0), style=shape.xcross, size=size.auto)

// Calculate LR High and Low
lrhigh = lr + multiplier * rss
lrlow = lr - multiplier * rss

atr21 = ta.atr(atrLength)
sqrtFactor = math.sqrt(21 * 0.5 * 0.5)
mixa = open + sqrtFactor * atr21
mixb = open - sqrtFactor * atr21
mixmid = (mixa + mixb) / 2

lrh = ta.linreg(mixa, 72, 0)
lrl = ta.linreg(mixb, 72, 0)

// Parabolic SAR
sar = ta.sar(0.01, 0.01, 0.1)
sarColor = close < sar ? color.white : #f3b5b5
//plot(sar, style=plot.style_circles, color=sarColor, title="Parabolic SAR", linewidth = 1)

// LWMA for ATR Bands
lwmaMixa = ta.wma(mixa, lwmaLength)
lwmaMixb = ta.wma(mixb, lwmaLength)

// Calculate sath and spath
var float sath = na
var float spath = na

if (mixa > lrhigh)
    sath := lrhigh
else if (mixb < lrlow)
    sath := lrlow
else
    sath := na

if (lwmaMixa > lrhigh)
    spath := lrhigh
else if (lwmaMixb < lrlow)
    spath := lrlow
else
    spath := na

// Find highest and lowest values in the last 224 and 384 bars
highestHigh224 = ta.highest(high, 224)[25]
highestHigh384 = ta.highest(high, 384)[25]
highestMixa224 = ta.highest(mixa, 224)[25]
highestMixa384 = ta.highest(mixa, 384)[25]
lowestLow224 = ta.lowest(low, 224)[25]
lowestLow384 = ta.lowest(low, 384)[25]
lowestMixb224 = ta.lowest(mixb, 224)[25]
lowestMixb384 = ta.lowest(mixb, 384)[25]

// Determine the highest high in the last 224 bars
highestHigh = highestHigh224
lowestLow = lowestLow224

// Find the bar index of the highest high in the last 224 bars
highestHighBarIndex = ta.highestbars(high, 224)[25]
lowestLowBarIndex = ta.lowestbars(low, 224)[25]
highestHighBarIndex2 = ta.highestbars(high, 384)[25]
lowestLowBarIndex2 = ta.lowestbars(low, 384)[25]

// Create a box from the highest high bar to the current bar
var box myBox224u = na
var box myBox384u = na
var box myBox224d = na
var box myBox384d = na

if (-highestHighBarIndex <= 249 and -highestHighBarIndex >= 25)
    if (not na(myBox224u))
        box.delete(myBox224u)
    myBox224u := box.new(left=bar_index - 25 + highestHighBarIndex, top=highestMixa224, right=bar_index, bottom=highestHigh, border_color=color.new(color.white, 50), border_width=2, bgcolor=color.new(color.gray, 50))
    myBox224u.set_right(bar_index + 1)

if (-lowestLowBarIndex <= 249 and -lowestLowBarIndex >= 25)
    if (not na(myBox224d))
        box.delete(myBox224d)
    myBox224d := box.new(left=bar_index - 25 + lowestLowBarIndex, top=lowestLow, right=bar_index + 1, bottom=lowestMixb224, border_color=color.new(color.white, 50), border_width=2, bgcolor = color.new(color.gray, 50))
    myBox224d.set_right(bar_index + 1)

if (-highestHighBarIndex2 <= 409 and -highestHighBarIndex2 >= 25 and highestHigh384 > highestMixa224)
    if (not na(myBox384u))
        box.delete(myBox384u)
    myBox384u := box.new(left=bar_index - 25 + highestHighBarIndex2, top=highestMixa384, right=bar_index + 1, bottom=highestHigh384, border_color=color.new(color.white, 50), border_width=2, bgcolor=color.new(color.yellow, 50))
    myBox384u.set_right(bar_index + 1)
else 
    if (not na(myBox384u))
        box.delete(myBox384u)

if (-lowestLowBarIndex2 <= 409 and -lowestLowBarIndex2 >= 25 and lowestMixb384 < lowestMixb224)
    if (not na(myBox384d))
        box.delete(myBox384d)
    myBox384d := box.new(left=bar_index - 25 + lowestLowBarIndex2, top=lowestLow384, right=bar_index + 1, bottom=lowestMixb384, border_color=color.new(color.white, 50), border_width=2, bgcolor = color.new(color.yellow, 50))
    myBox384d.set_right(bar_index + 1)
else 
    if (not na(myBox384d))
        box.delete(myBox384d)

lengtha = 180
ema1 = ta.ema(close, lengtha)
ema2 = ta.ema(ema1, lengtha)
ema3 = ta.ema(ema2, lengtha)
out = 3 * (ema1 - ema2) + ema3

//ma20 = request.security(syminfo.tickerid, "60", ta.ema(close, 20))
// Plotting
//plot(lrh, color=lrh > ma20 and lrl > ma20 ? color.blue : lrh < ma20 and lrl < ma20 ? color.red : color.white, linewidth = 2, title="LR High")
//plot(lrl, color=lrh > ma20 and lrl > ma20 ? color.blue : lrh < ma20 and lrl < ma20 ? color.red : color.white, linewidth = 2, title="LR Low")
p2 = plot(lrh, color=color.white, linewidth = 2, title="LR High")
p4 = plot(lrl, color=color.white, linewidth = 2, title="LR Low")

p1 = plot(lwmaMixa, title="", color=lwmaMixa >= lrh ? color.new(color.yellow, 90) : color.new(color.yellow, 100))
p3 = plot(lwmaMixb, title="", color=lwmaMixb <= lrl ? color.new(color.yellow, 90) : color.new(color.yellow, 100))

fill(p1, p2, color= lwmaMixa >= lrh ? color.new(color.white, 70) : na)
fill(p3, p4, color= lwmaMixb <= lrl ? color.new(color.white, 70) : na)

//slopeup = ta.linreg(lrh, 20, 0)
//slopedn = ta.linreg(lrl, 20, 0)

//arrdn = slopedn > slopedn[1] and slopeup > slopeup[1] and open[1] > lrh and open > lrh and high < lwmaMixa
//plotshape(arrdn, title='ARRU', style=shape.triangleup, color=color.blue, location=location.belowbar)
//arrup = slopeup < slopeup[1] and slopedn < slopedn[1] and open[1] < lrl and open < lrl and low > lwmaMixb
//plotshape(arrup, title='ARRD', style=shape.triangledown, color=color.red, location=location.abovebar)

rsi = ta.rsi(close, 14)
entry_rsiu = open > out and low < out and rsi > 50
entry_rsid = open < out and high > out and rsi < 50

//plotshape(entry_rsiu, title = 'CU', style = shape.circle, location = location.belowbar, size = size.tiny, color = color.blue)
//plotshape(entry_rsid, title = 'CD', style = shape.circle, location = location.abovebar, size = size.tiny, color = color.red)
//
//plot(lr, color=color.blue, title="Linear Regression")
//plot(lrhigh, color=color.red, title="LR High")
//plot(lrlow, color=color.green, title="LR Low")
//plot(mixa, color=color.yellow, title="Mixa")
//plot(mixb, color=color.yellow, title="Mixb")
//plot(lwmaMixa, color=color.new(color.yellow, 30), title="LWMA Mixa")
//plot(lwmaMixb, color=color.new(color.yellow, 30), title="LWMA Mixb")
plot(not na(sath) ? sath : na, color=color.purple, title="SATH", style=plot.style_circles, linewidth=2, offset=-1)
plot(not na(spath) ? spath : na, color=color.aqua, title="SPATH", style=plot.style_circles, linewidth=2, offset=-1)


var float toparr = na
var float botarr = na
var float putarr = na
var float sutarr = na
var float kutarr = na

// Calculate torop
torop1 = 0.2 * (highestMixa224 - lowestMixb224)
torop2 = 0.2 * (highestMixa384 - lowestMixb384)

// Loop through the range
for y = math.max(-highestHighBarIndex, -lowestLowBarIndex) to 1
    if (y != -highestHighBarIndex and y != -lowestLowBarIndex and y < -highestHighBarIndex and mixa[y] > mixa[y + 1] and mixa[y] > lwmaMixa[y] and mixa[y] > highestMixa224 - torop1)
        toparr := mixa[y]
    else 
        toparr := na
    if (y != -highestHighBarIndex and y != -lowestLowBarIndex and y < -lowestLowBarIndex and mixb[y] < mixb[y + 1] and mixb[y] < lwmaMixb[y] and mixb[y] < lowestMixb224 + torop1)
        botarr := mixb[y] 
    else 
        botarr := na
    if (y != -highestHighBarIndex and y != -lowestLowBarIndex and mixa[y + 2] < lr[y + 2] and mixa[y + 1] > lr[y + 1] and mixa[y] < lr[y])
        putarr := high[y]
    else
        putarr := na
    if (y != -highestHighBarIndex and y != -lowestLowBarIndex and mixb[y + 2] > lr[y + 2] and mixb[y + 1] < lr[y + 1] and mixb[y] > lr[y])
        putarr := low[y]
    else
        putarr := na
    if (low[y] < lwmaMixb[y])
        sutarr := high[y]
    else
        sutarr := na
    if (high[y] > lwmaMixa[y])
        kutarr := low[y]
    else
        kutarr := na
    
plotshape(not na(toparr) ? toparr : na, location=location.absolute, color=color.new(#e174f5bd, 30), title="top", style=shape.circle, size = size.tiny, show_last=224, offset = -1)

plotshape(not na(botarr) ? botarr : na, location=location.absolute, color=color.new(#54fc629c, 30), title="bot", style=shape.circle, size = size.tiny, show_last=224, offset = -1)

plotshape(not na(putarr) ? putarr : na, location=location.absolute, color=color.new(color.rgb(243, 227, 0), 0), title="bot", style=shape.circle, size = size.tiny, show_last=224, offset = -1)

//plotshape(not na(sutarr) ? sutarr : na, location=location.abovebar, color=color.new(#fcf3a7b3, 50), title="stop", style=shape.triangledown, size = size.tiny, show_last=224, offset = -1)

//plotshape(not na(kutarr) ? kutarr : na, location=location.belowbar, color=color.new(#fcf3a7b3, 50), title="stop", style=shape.triangleup, size = size.tiny, show_last=224, offset = -1)

// Linear Regression Channel Function (Modified)
calcSlope(source, length) =>
    max_bars_back(source, 5000)
    if not barstate.islast or length <= 1
        [float(na), float(na), float(na)]
    else
        sumX = 0.0
        sumY = 0.0
        sumXSqr = 0.0
        sumXY = 0.0
        for i = 0 to length - 1 by 1
            val = source[i]
            per = i
            sumX += per
            sumY += val
            sumXSqr += per * per
            sumXY += val * per
        slope = (length * sumXY - sumX * sumY) / (length * sumXSqr - sumX * sumX) // a
        average = sumY / length
        intercept = average - slope * sumX / length + slope
        [slope, average, intercept]
        
calcDev(source, length, slope, average, intercept) =>
    upDev = 0.0
    dnDev = 0.0
    stdDevAcc = 0.0
    dsxx = 0.0
    dsyy = 0.0
    dsxy = 0.0
    periods = length - 1
    daY = intercept + slope * periods / 2
    val = intercept
    for j = 0 to periods by 1
        mprice = val
        if mprice > upDev
            upDev := mprice
        mprice := val
        if mprice > dnDev
            dnDev := mprice
        mprice := source[j]
        dxt = mprice - average
        dyt = val - daY
        mprice -= val
        stdDevAcc += mprice * mprice
        dsxx += dxt * dxt
        dsyy += dyt * dyt
        dsxy += dxt * dyt
        val += slope
    stdDev = math.sqrt(stdDevAcc / (periods == 0 ? 1 : periods))
    pearsonR = dsxx == 0 or dsyy == 0 ? 0 : dsxy / math.sqrt(dsxx * dsyy)
    [stdDev, pearsonR, upDev, dnDev]

// Function to draw the channel (Modified)
drawChannel(sourceUpper, sourceLower, length, upperMult, lowerMult, colorUpper, colorLower, wid) =>
    [sUpper, aUpper, iUpper] = calcSlope(sourceUpper, length)  // Slope for mixa (upper)
    startPriceUpper = iUpper + sUpper * (length - 1)
    endPriceUpper = iUpper

    [stdDevUpper, pearsonRUpper, upDevUpper, dnDevUpper] = calcDev(sourceUpper, length, sUpper, aUpper, iUpper)
    upperStartPrice = startPriceUpper + upperMult * stdDevUpper
    upperEndPrice = endPriceUpper + upperMult * stdDevUpper
    var line upper = na

    [sLower, aLower, iLower] = calcSlope(sourceLower, length)  // Slope for mixb (lower)
    startPriceLower = iLower + sLower * (length - 1)
    endPriceLower = iLower

    [stdDevLower, pearsonRLower, upDevLower, dnDevLower] = calcDev(sourceLower, length, sLower, aLower, iLower)
    lowerStartPrice = startPriceLower - lowerMult * stdDevLower // Use sourceLower for lower calculations
    lowerEndPrice = endPriceLower - lowerMult * stdDevLower     // Use sourceLower for lower calculations
    var line lower = na

    if na(upper) and not na(upperStartPrice)
        upper := line.new(bar_index - length + 1, upperStartPrice, bar_index, upperEndPrice, width=wid, extend=extend.none, color=colorUpper)
    else
        line.set_xy1(upper, bar_index - length + 1, upperStartPrice)
        line.set_xy2(upper, bar_index, upperEndPrice)
        na

    if na(lower) and not na(lowerStartPrice)
        lower := line.new(bar_index - length + 1, lowerStartPrice, bar_index, lowerEndPrice, width=wid, extend=extend.none, color=colorLower)
    else
        line.set_xy1(lower, bar_index - length + 1, lowerStartPrice)
        line.set_xy2(lower, bar_index, lowerEndPrice)
        na

getUpperChannelValue(source, length) =>
    [sUpper, aUpper, iUpper] = calcSlope(source, length)  // Slope for mixa (upper)
    startPriceUpper = iUpper + sUpper * (length - 1)
    endPriceUpper = iUpper

    [stdDevUpper, pearsonRUpper, upDevUpper, dnDevUpper] = calcDev(source, length, sUpper, aUpper, iUpper)
    upperStartPrice = startPriceUpper + stdDevUpper // Use source for upper calculations
    upperEndPrice = endPriceUpper + stdDevUpper     // Use source for upper calculations

    upperEndPrice // Return the upper end price as the upper channel value

getLowerChannelValue(source, length) =>
    [sLower, aLower, iLower] = calcSlope(source, length)  // Slope for mixb (lower)
    startPriceLower = iLower + sLower * (length - 1)
    endPriceLower = iLower

    [stdDevLower, pearsonRLower, upDevLower, dnDevLower] = calcDev(source, length, sLower, aLower, iLower)
    lowerStartPrice = startPriceLower - stdDevLower // Use source for lower calculations
    lowerEndPrice = endPriceLower - stdDevLower     // Use source for lower calculations

    lowerEndPrice // Return the lower end price as the lower channel value

// Channel 3
length3 = 240
upper3 = getUpperChannelValue(mixa, length3)
lower3 = getLowerChannelValue(mixb, length3)
mid3 = (upper3 + lower3) / 2
drawChannel(mixa, mixb, length3, 1.0, 1.0, color.rgb(31, 45, 247), color.rgb(31, 45, 247), 3)
drawChannel(mixmid, mixmid, length3, 0, 0, color.aqua, color.aqua, 2)
// Channel 2
length2 = 120
upper2 = getUpperChannelValue(mixa, length2)
lower2 = getLowerChannelValue(mixb, length2)
mid2 = (upper2 + lower2) / 2
drawChannel(mixa, mixb, length2, 1.0, 1.0, color.rgb(175, 55, 7), color.rgb(175, 55, 7), 3)
//drawChannel(mixmid, mixmid, length2, 0, 0, #bb4416, #bb4416, 1)
// Channel 1
length1 = 72
upper1 = getUpperChannelValue(mixa, length1)
lower1 = getLowerChannelValue(mixb, length1)
mid1 = (upper1 + lower1) / 2
drawChannel(mixa, mixb, length1, 1.0, 1.0, #e7bb68, #e7bb68, 3)
// Channel 0
length0 = 24
//drawChannel(mixa, mixb, length0, 1.0, 1.0, color.purple, color.purple, 2)

//plotshape(series=upper1 > upper2 and upper2 > upper3, location=location.belowbar, color=color.blue, style=shape.circle, size=size.large, title="Upper Exceeding")
//plotshape(series=lower1 < lower2 and lower2 < lower3, location=location.abovebar, color=color.red, style=shape.circle, size=size.large, title="Lower Exceeding")

// Variable to store the label
var label conditionLabel = na
var label conditionLabel1 = na

// Delete the previous label if it exists
if (not na(conditionLabel))
    label.delete(conditionLabel)
if (not na(conditionLabel1))
    label.delete(conditionLabel1)

//if (upper1 > upper2 and upper2 > upper3)
//    conditionLabel := label.new(x=bar_index, y=open, text="", style=label.style_circle, color=color.blue, size=size.tiny)
//else if (lower1 > lower2 and lower2 < lower3)
    //conditionLabel := label.new(x=bar_index, y=open, text="", style=label.style_circle, color=color.green, size=size.tiny)
//else if (lower1 < lower2 and lower2 < lower3)
//    conditionLabel := label.new(x=bar_index, y=open, text="", style=label.style_circle, color=color.red, size=size.tiny)
//else if (upper1 < upper2 and upper2 > upper3)
    //conditionLabel := label.new(x=bar_index, y=open, text="", style=label.style_circle, color=color.orange, size=size.tiny)

//if (mid1 > mid2 and mid2 > mid3)
//    conditionLabel1 := label.new(x=bar_index, y=open, text="", style=label.style_circle, color=color.yellow, size=size.tiny)
//else if (mid1 < mid2 and mid2 < mid3)
//    conditionLabel1 := label.new(x=bar_index, y=open, text="", style=label.style_circle, color=color.new(#87898f, 0), size=size.tiny)

// Move the label to the bottom right of the screen
//if (not na(conditionLabel))
//    label.set_xy(conditionLabel, x=bar_index + 1, y=low)