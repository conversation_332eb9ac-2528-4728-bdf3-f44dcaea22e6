//@version=6
indicator("deezwave", overlay = true)

// Input fields for customization
lookbackPeriod = input.int(120, title="Lookback Period for High/Low")
seriesIncrement = input.float(0.001, title="Series Increment/Decrement Value")

// Create custom series based on close price comparison
var float customSeries = na
//customSeries := na(customSeries[1]) ? 0 : customSeries[1] + (close > close[1] ? seriesIncrement : -seriesIncrement)

//flot = ta.sma(customSeries, 6)

// Calculate high and low over the lookback period
highLookback = request.security(syminfo.tickerid, "D", high)[1]
lowLookback = request.security(syminfo.tickerid, "D", low)[1]

dist = highLookback - lowLookback
lowdist = lowLookback + 0.236 * dist
higdist = highLookback - 0.236 * dist
llow = lowLookback - 0.618 * dist
hhig = highLookback + 0.618 * dist
llow1 = lowLookback - dist
hhig1 = highLookback + dist
llow2 = lowLookback - 1.618 * dist
hhig2 = highLookback + 1.618 * dist
middist = lowLookback + ((highLookback - lowLookback) / 2)

drop = (open[1] < lowdist[1] or close[1] < lowdist[1]) and open < lowdist
rise = (open[1] > higdist[1] or close[1] > higdist[1]) and open > higdist
exceedu = (close[1] < highLookback[1] and close > highLookback) or (close[1] > highLookback[1] and close > highLookback)
exceedd = (close[1] > lowLookback[1] and close < lowLookback) or (close[1] < lowLookback[1] and close < lowLookback)
exceedu2 = close[1] > hhig[1]
exceedd2 = close[1] < llow[1]

//bgcolor(exceedu2 ? color.new(color.navy, 50) : exceedd2 ? color.new(color.maroon, 50) : exceedu ? color.new(color.green, 80) : exceedd ? color.new(color.orange, 80) : drop ? color.new(color.red, 80) : rise ? color.new(color.blue, 80) : na)

// Plot custom series as a line
p1 = plot(highLookback, title="MainH", color=color.new(color.white, 0), linewidth=1)
p2 = plot(lowLookback, title="MainL", color=color.new(color.white, 0), linewidth=1)
p3 = plot(lowdist, title="L23", color= color.new(color.yellow, 75), linewidth=1)
p4 = plot(higdist, title="H23", color= color.new(color.yellow, 75), linewidth=1)
p5 = plot(llow, title="LExt", color= color.new(color.yellow, 75), linewidth=1)
p6 = plot(hhig, title="HExt", color= color.new(color.yellow, 75), linewidth=1)
p7 = plot(llow1, title="LExt1", color= color.new(color.white, 75), linewidth=1)
p8 = plot(hhig1, title="HExt1", color= color.new(color.white, 75), linewidth=1)
p9 = plot(llow2, title="LExt2", color= color.new(color.yellow, 75), linewidth=1)
p10 = plot(hhig2, title="HExt2", color= color.new(color.yellow, 75), linewidth=1)
//plot(llow, title="Custom Series2", color= color.gray, linewidth=1)
//plot(hhig, title="Custom Series2", color= color.gray, linewidth=1)
plot(middist, title="Custom Series2", color=color.aqua, linewidth=2)

fill(p1, p4, color = color.new(color.gray, 70))
fill(p2, p3, color = color.new(color.gray, 70))
fill(p5, p2, color = color.new(color.yellow, 70))
fill(p6, p1, color = color.new(color.yellow, 70))
fill(p5, p7, color = color.new(color.aqua, 70))
fill(p6, p8, color = color.new(color.aqua, 70))
fill(p7, p9, color = color.new(color.silver, 90))
fill(p8, p10, color = color.new(color.silver, 90))