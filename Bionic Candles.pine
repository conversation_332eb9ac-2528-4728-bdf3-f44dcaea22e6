// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © algostudio
// Code Generated using PineGPT - www.marketcalls.in

//@version=6
indicator("Bionic Candles", overlay=true, behind_chart = false, explicit_plot_zorder = true)

// Display bionic candle zones using filled shapes
maxhigh = close == high //close > close[1] and (low == close[1] or low > close[1] or close[1] - low < syminfo.mintick) and close - low > 0.9 * (high - low)
maxlow = close == low //close < close[1] and (high == close[1] or high < close[1] or high - close[1] < syminfo.mintick) and high - close > 0.9 * (high - low)
higher = close > close[1]
lower = close < close[1]

plotcandle(close > close[1] ? high : low, high, low, close, color = maxhigh ? #c4b000 : maxlow ? #384eb1: higher ? #c20000 : lower ? #009b05 : color.white, wickcolor = #808080, bordercolor = maxhigh ? #c4b000 : maxlow ? #384eb1: higher ? #c20000 : lower ? #009b05 : color.white)
plotcandle(close > close[1] ? low : high, close > close[1] ? close : high, close > close[1] ? low : high, close, color = maxhigh ? #c4b000 : maxlow ? #384eb1 : close > close[1] or close < close[1] ? #808080 : color.white, bordercolor = maxhigh ? #c4b000 : maxlow ? #384eb1 : close > close[1] or close < close[1] ? #808080 : color.white)
//plotcandle(close > close[1] and close = low ?, close > close[1] ? close : high, close > close[1] ? low : high, close, color = color.gray)