//@version=6
indicator("LR Channels with Custom Bands", overlay=true)

// Input parameters
times = 288
timesatr = 21
multiplier = input.float(2.0, "Multiplier")
shift = input.int(0, "Shift")

// New input parameters
timesb = 72
timesc = 240
timesa = 120

// Arrays to store LR channel values
var float[] trophya = na
var float[] trophyb = na
var float[] trigga = na
var float[] triggb = na
var float[] trophyc = na
var float[] trophyd = na
var float[] trifd72 = na
var float[] trofd72 = na

// Initialize arrays
if na(trophya)
    trophya := array.new_float(500, na)
    trophyb := array.new_float(500, na)
    trigga := array.new_float(500, na)
    triggb := array.new_float(500, na)
    trophyc := array.new_float(500, na)
    trophyd := array.new_float(500, na)
    trifd72 := array.new_float(500, na)
    trofd72 := array.new_float(500, na)

// LWMA Custom Weighted Function
get_lwma(src, period, weight) =>
    price = src
    sub = (weight / period) - 1
    float p = na
    float d = na
    float sum = 0
    float divider = 0
    for i = 0 to period - 1
        p := price[i] * ((weight - i) - sub)
        d := (weight - i) - sub
        sum := sum + p
        divider := divider + d
    sum / divider

// Function to calculate Linear Regression
calcLR(src, length) =>
    var float summix = 0.0
    var float summiy = 0.0
    var float summixy = 0.0
    var float summix2 = 0.0
    
    summix := 0.0
    summiy := 0.0
    summixy := 0.0
    summix2 := 0.0
    
    for i = 0 to length - 1
        summix := summix + i
        summiy := summiy + src[i]
        summixy := summixy + i * src[i]
        summix2 := summix2 + i * i
    
    lr2 = (length * summixy - summix * summiy) / (length * summix2 - summix * summix)
    yinttt = (summiy - lr2 * summix) / length
    
    [yinttt, lr2]

// Calculate RSS (Residual Sum of Squares)
calcRSS(src, basis, length) =>
    var float rss = 0.0
    rss := 0.0
    
    for i = 0 to length - 1
        rss := rss + math.pow(src[i] - basis[i], 2)
    
    math.sqrt(rss / (length - 1))

// Function to calculate Linear Regression and store in array
calculateLinearRegression(src, tro, period) =>
    var float isumx = 0.0
    var float isumy = 0.0
    var float isumxy = 0.0
    var float isumx2 = 0.0

    for j = 0 to period - 1
        isumy := isumy + src[j]
        isumxy := isumxy + src[j] * j
        isumx := isumx + j
        isumx2 := isumx2 + j * j

    c = isumx2 * period - isumx * isumx
    a = (isumxy * period - isumx * isumy) / c
    b = (isumy - isumx * a) / period

    for j = 0 to period - 1
        array.set(tro, j, a * j + b)

// Main calculations
[dylanM, slope] = calcLR(close, times)
rss = calcRSS(close, dylanM, times)

dylanH = dylanM + multiplier * rss
dylanL = dylanM - multiplier * rss

// ATR-based calculations
atr21 = ta.atr(timesatr)
mixa = open + math.sqrt(timesatr * 0.5 * 0.5) * atr21
mixb = open - math.sqrt(timesatr * 0.5 * 0.5) * atr21

// Moving averages of the bands
outsiderima = get_lwma(mixa, 10, 10)
outsiderimb = get_lwma(mixb, 10, 10)

// Signals
var float sath = na
var float spath = na

if mixa > dylanH
    sath := dylanH
if mixb < dylanL
    sath := dylanL
if outsiderima > dylanH
    spath := dylanH
if outsiderimb < dylanL
    spath := dylanL

// Calculate LR channels
calculateLinearRegression(mixa, trophya, timesb)
calculateLinearRegression(mixb, trophyb, timesb)
calculateLinearRegression(mixa, trophyc, timesa)
calculateLinearRegression(mixb, trophyd, timesa)
calculateLinearRegression(mixa, trigga, timesc)
calculateLinearRegression(mixb, triggb, timesc)

// Plot the results
plot(dylanM, "Dylan Middle", color=color.blue)
plot(dylanH, "Dylan High", color=color.red)
plot(dylanL, "Dylan Low", color=color.red)
plot(outsiderima, "Upper LWMA", color=color.purple)
plot(outsiderimb, "Lower LWMA", color=color.purple)
plot(mixa, "MIXA", color = color.yellow)
plot(mixb, "MIXB", color = color.yellow)

// Plot LR channels
plot(array.get(trophya, 0), "Trophya", color=color.new(color.yellow, 0), linewidth=3)
plot(array.get(trophyb, 0), "Trophyb", color=color.new(color.yellow, 0), linewidth=3)
plot(array.get(trigga, 0), "Trigga", color=color.new(color.blue, 0), linewidth=1)
plot(array.get(triggb, 0), "Triggb", color=color.new(color.red, 0), linewidth=1)
plot(array.get(trophyc, 0), "Trophyc", color=color.new(color.yellow, 0), linewidth=2)
plot(array.get(trophyd, 0), "Trophyd", color=color.new(color.yellow, 0), linewidth=2)
plot(array.get(trifd72, 0), "Trifd72", color=color.new(color.yellow, 0), linewidth=1)
plot(array.get(trofd72, 0), "Trofd72", color=color.new(color.yellow, 0), linewidth=1)