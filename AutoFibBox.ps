//@version=6
indicator('AutoFibBox', overlay = true)

// Variables to store daily high, low, and their bar indices
var float dayHigh = na
var float dayLow = na
var int highIndex = na
var int lowIndex = na

// Variables to store daily high, low, and their bar indices
var float dayHigh1 = na
var float dayLow1 = na
var int highIndex1 = na
var int lowIndex1 = na

// Calculate bar index for start and end of the day
var int dayStartIndex = na
var int dayEndIndex = na

var float MINUTES_IN_DAY = 1440.0
var float barsPerDay = MINUTES_IN_DAY / timeframe.multiplier

// Add variables for 10-hour tracking
var float MINUTES_IN_10_HOURS = 599  // 10 hours * 60 minutes
var float barsIn10Hours = MINUTES_IN_10_HOURS / timeframe.multiplier
var int dayStart10HourIndex = na

// Add variables to store line IDs
var line highLine = na
var line midLine = na
var line lowLine = na

// Add box tracking variables
//var box upperBox = na
//var box lowerBox = na
var int currentDayIndex = na

// Add variables for tracking 10-hour boxes
var box[] tenHourBoxes = array.new_box()
var box[] lowerBoxes = array.new_box()
var box[] upperBoxes = array.new_box()
var line[] tenHourMidLine = array.new_line()
var int maxHistoryDays = 10

// Add variables for extension boxes
var box upperExtBox = na
var box lowerExtBox = na

// Add variable for mid price line
//var line tenHourMidLine = na
// Step 1: Initialize arrays and a day counter
var int dayCounter = 0
var dailyHighs = array.new_float()
var dailyHighIndices = array.new_int()
var dailyLows = array.new_float()
var dailyLowIndices = array.new_int()

// Step 1: Store the last pivot to make the line continuous
var float lastPivotPrice = na
var int   lastPivotIndex = na

// Variables to store the lines
var line line_261_8_neg = na
var line line_200_neg = na
var line line_161_8_neg = na
var line line_100_neg = na
var line line_61_8_neg = na
var line line_55_9_neg = na
var line line_50_neg = na
var line line_44_1_neg = na
var line line_30_9_neg = na
var line line_25_neg = na
var line line_19_1_neg = na
var line line_5_9_neg = na
var line line_0 = na
var line line_5_9 = na
var line line_19_1 = na
var line line_25 = na
var line line_30_9 = na
var line line_44_1 = na
var line line_50 = na
var line line_55_9 = na
var line line_69_1 = na
var line line_75 = na
var line line_80_9 = na
var line line_94_1 = na
var line line_100 = na
var line line_105_9 = na
var line line_119_1 = na
var line line_125 = na
var line line_130_9 = na
var line line_144_1 = na
var line line_150 = na
var line line_155_9 = na
var line line_161_8 = na
var line line_200 = na
var line line_261_8 = na
var line line_300 = na
var line line_361_8 = na

// Variables to store the labels
var label label_50_neg = na
var label label_25_neg = na
var label label_0 = na
var label label_25 = na
var label label_50 = na
var label label_75 = na
var label label_100 = na
var label label_125 = na
var label label_150 = na


// Check if a new day has started
newDay = ta.change(time('D'))
if bool(newDay)
    // Update day start/end indices
    dayStartIndex := bar_index
    dayEndIndex := dayStartIndex + int(barsPerDay) // Calculate bars dynamically based on timeframe
    dayStart10HourIndex := bar_index

    // Draw the line for the completed day
    if not na(dayHigh) and not na(dayLow)
        //if lowIndex < highIndex
        //    line.new(x1 = lowIndex, y1 = dayLow, x2 = highIndex, y2 = dayHigh, color = color.blue, width = 3)
        //else
        //    line.new(x1 = highIndex, y1 = dayHigh, x2 = lowIndex, y2 = dayLow, color = color.orange, width = 3)

        // Add labels for the high and low
        //label.new(x = highIndex, y = dayHigh, text = 'High: ' + str.tostring(dayHigh, format.mintick), style = label.style_label_down, color = color.new(color.red, 70), textcolor = color.black)
        //label.new(x = lowIndex, y = dayLow, text = 'Low: ' + str.tostring(dayLow, format.mintick), style = label.style_label_up, color = color.new(color.green, 70), textcolor = color.black)

        // Calculate Fibonacci regions
        fibHigh = math.max(dayHigh, dayLow)
        fibLow = math.min(dayHigh, dayLow)
        fib_618 = fibLow + (fibHigh - fibLow) * 0.618
        fib_764 = fibLow + (fibHigh - fibLow) * 0.764
        fib_382 = fibLow + (fibHigh - fibLow) * 0.382
        fib_236 = fibLow + (fibHigh - fibLow) * 0.236
        
        if array.size(upperBoxes) >= maxHistoryDays
            box.delete(array.shift(upperBoxes))
            box.delete(array.shift(lowerBoxes))

        // Draw Fibonacci regions for the next day
        //boxup = box.new(left = dayStartIndex, right = dayEndIndex, top = fib_764, bottom = fib_618, bgcolor = color.new(color.yellow, 70), border_color = color.new(color.yellow, 70), border_width = 1)//, extend = extend.none)
        //boxdn = box.new(left = dayStartIndex, right = dayEndIndex, top = fib_382, bottom = fib_236, bgcolor = color.new(#dfa6a600, 70), border_color = color.new(#eb9b9b, 70), border_width = 1)//, extend = extend.none)

        // Draw new boxes
        //upperBox = box.new(left=dayStartIndex, right=dayEndIndex, top=fib_764, bottom=fib_618, bgcolor=color.new(color.yellow, 85), border_color=color.yellow, border_width=2)//, extend=extend.none)
        //lowerBox = box.new(left=dayStartIndex, right=dayEndIndex, top=fib_382, bottom=fib_236, bgcolor=color.new(#dfa6a600, 85), border_color=color.rgb(223, 166, 166), border_width=2)//, extend=extend.none)
        
        //array.push(upperBoxes, boxup)        
        //array.push(lowerBoxes, boxdn)

        // Make previous boxes transparent
        //if not na(upperBox)
        //    box.set_bgcolor(upperBox, color.new(color.yellow, 100))
        //    box.set_bgcolor(lowerBox, color.new(#dfa6a600, 100))

        currentDayIndex := dayStartIndex

if bool(newDay)
    dayCounter += 1
    array.push(dailyHighs, dayHigh1)
    array.push(dailyHighIndices, highIndex1)
    array.push(dailyLows, dayLow1)
    array.push(dailyLowIndices, lowIndex1)

    // Step 2: Every 3 days, find highest/lowest
    if dayCounter % 3 == 0
        float threeDayHigh = -10e10
        float threeDayLow = 10e10
        int threeDayHighIndex = na
        int threeDayLowIndex = na

        for i = 0 to 2
            float h = array.get(dailyHighs, array.size(dailyHighs) - 1 - i)
            float l = array.get(dailyLows, array.size(dailyLows) - 1 - i)
            int hi = array.get(dailyHighIndices, array.size(dailyHighIndices) - 1 - i)
            int li = array.get(dailyLowIndices, array.size(dailyLowIndices) - 1 - i)
            if h > threeDayHigh
                threeDayHigh := h
                threeDayHighIndex := hi
            if l < threeDayLow
                threeDayLow := l
                threeDayLowIndex := li

        // Step 3: Connect pivots in time order from the last pivot
        if threeDayHighIndex < threeDayLowIndex
            //if not na(lastPivotPrice)
                //line.new(lastPivotIndex, lastPivotPrice, threeDayHighIndex, threeDayHigh, color=color.blue, width=2)
            //line.new(threeDayHighIndex, threeDayHigh, threeDayLowIndex, threeDayLow, color=color.blue, width=2)
            lastPivotPrice := threeDayLow
            lastPivotIndex := threeDayLowIndex
        else
            //if not na(lastPivotPrice)
                //line.new(lastPivotIndex, lastPivotPrice, threeDayLowIndex, threeDayLow, color=color.blue, width=2)
            //line.new(threeDayLowIndex, threeDayLow, threeDayHighIndex, threeDayHigh, color=color.blue, width=2)
            lastPivotPrice := threeDayHigh
            lastPivotIndex := threeDayHighIndex



        // Calculate the levels based on the last drawn threeDayHigh and threeDayLow
        level_261_8_neg = threeDayLow + (threeDayHigh - threeDayLow) * -2.618
        level_200_neg = threeDayLow + (threeDayHigh - threeDayLow) * -2
        level_161_8_neg = threeDayLow + (threeDayHigh - threeDayLow) * -1.618
        level_100_neg = threeDayLow + (threeDayHigh - threeDayLow) * -1
        level_61_8_neg = threeDayLow + (threeDayHigh - threeDayLow) * -0.618
        level_50_neg = threeDayLow + (threeDayHigh - threeDayLow) * -0.5
        level_25_neg = threeDayLow + (threeDayHigh - threeDayLow) * -0.25
        level_5_9_neg = threeDayLow + (threeDayHigh - threeDayLow) * -0.059
        level_5_9 = threeDayLow + (threeDayHigh - threeDayLow) * 0.059
        level_25 = threeDayLow + (threeDayHigh - threeDayLow) * 0.25
        level_44_1 = threeDayLow + (threeDayHigh - threeDayLow) * 0.441
        level_50 = threeDayLow + (threeDayHigh - threeDayLow) * 0.5
        level_55_9 = threeDayLow + (threeDayHigh - threeDayLow) * 0.559
        level_75 = threeDayLow + (threeDayHigh - threeDayLow) * 0.75
        level_94_1 = threeDayLow + (threeDayHigh - threeDayLow) * 0.941
        level_105_9 = threeDayLow + (threeDayHigh - threeDayLow) * 1.059
        level_125 = threeDayLow + (threeDayHigh - threeDayLow) * 1.25
        level_150 = threeDayLow + (threeDayHigh - threeDayLow) * 1.5
        level_161_8 = threeDayLow + (threeDayHigh - threeDayLow) * 1.618
        level_200 = threeDayLow + (threeDayHigh - threeDayLow) * 2
        level_261_8 = threeDayLow + (threeDayHigh - threeDayLow) * 2.618
        level_300 = threeDayLow + (threeDayHigh - threeDayLow) * 3
        level_361_8 = threeDayLow + (threeDayHigh - threeDayLow) * 3.618

        // Calculate new levels
        level_55_9_neg = threeDayLow + (threeDayHigh - threeDayLow) * -0.559
        level_44_1_neg = threeDayLow + (threeDayHigh - threeDayLow) * -0.441  
        level_30_9_neg = threeDayLow + (threeDayHigh - threeDayLow) * -0.309
        level_19_1_neg = threeDayLow + (threeDayHigh - threeDayLow) * -0.191
        level_0 = threeDayLow + (threeDayHigh - threeDayLow) * 0
        level_19_1 = threeDayLow + (threeDayHigh - threeDayLow) * 0.191
        level_30_9 = threeDayLow + (threeDayHigh - threeDayLow) * 0.309
        level_69_1 = threeDayLow + (threeDayHigh - threeDayLow) * 0.691
        level_80_9 = threeDayLow + (threeDayHigh - threeDayLow) * 0.809
        level_100 = threeDayLow + (threeDayHigh - threeDayLow) * 1.0
        level_119_1 = threeDayLow + (threeDayHigh - threeDayLow) * 1.191
        level_130_9 = threeDayLow + (threeDayHigh - threeDayLow) * 1.309
        level_144_1 = threeDayLow + (threeDayHigh - threeDayLow) * 1.441
        level_155_9 = threeDayLow + (threeDayHigh - threeDayLow) * 1.559

        // Determine the starting index for the lines
        startIndex = na(threeDayHighIndex) ? threeDayLowIndex : threeDayHighIndex

        // Delete previous lines and labels
        if not na(line_261_8_neg)
            line.delete(line_261_8_neg)
        if not na(line_200_neg)
            line.delete(line_200_neg)
        if not na(line_161_8_neg)
            line.delete(line_161_8_neg)
        if not na(line_100_neg)
            line.delete(line_100_neg)
        if not na(line_61_8_neg)
            line.delete(line_61_8_neg)
        if not na(line_55_9_neg)
            line.delete(line_55_9_neg)
        if not na(line_50_neg)
            line.delete(line_50_neg)
        if not na(line_44_1_neg)
            line.delete(line_44_1_neg)
        if not na(line_30_9_neg)
            line.delete(line_30_9_neg)
        if not na(line_25_neg)
            line.delete(line_25_neg)
        if not na(line_19_1_neg)
            line.delete(line_19_1_neg)
        if not na(line_5_9_neg)
            line.delete(line_5_9_neg)
        if not na(line_0)
            line.delete(line_0)
        if not na(line_5_9)
            line.delete(line_5_9)
        if not na(line_19_1)
            line.delete(line_19_1)
        if not na(line_25)
            line.delete(line_25)
        if not na(line_30_9)
            line.delete(line_30_9)
        if not na(line_44_1)
            line.delete(line_44_1)
        if not na(line_50)
            line.delete(line_50)
        if not na(line_55_9)
            line.delete(line_55_9)
        if not na(line_69_1)
            line.delete(line_69_1)
        if not na(line_75)
            line.delete(line_75)
        if not na(line_80_9)
            line.delete(line_80_9)
        if not na(line_94_1)
            line.delete(line_94_1)
        if not na(line_100)
            line.delete(line_100)
        if not na(line_105_9)
            line.delete(line_105_9)
        if not na(line_119_1)
            line.delete(line_119_1)
        if not na(line_125)
            line.delete(line_125)
        if not na(line_130_9)
            line.delete(line_130_9)
        if not na(line_144_1)
            line.delete(line_144_1)
        if not na(line_150)
            line.delete(line_150)
        if not na(line_155_9)
            line.delete(line_155_9)
        if not na(line_161_8)
            line.delete(line_161_8)
        if not na(line_200)
            line.delete(line_200)
        if not na(line_261_8)
            line.delete(line_261_8)
        if not na(line_300)
            line.delete(line_300)
        if not na(line_361_8)
            line.delete(line_361_8)

        if not na(label_50_neg)
            label.delete(label_50_neg)
        if not na(label_25_neg)
            label.delete(label_25_neg)
        if not na(label_0)
            label.delete(label_0)
        if not na(label_25)
            label.delete(label_25)
        if not na(label_50)
            label.delete(label_50)
        if not na(label_75)
            label.delete(label_75)
        if not na(label_100)
            label.delete(label_100)
        if not na(label_125)
            label.delete(label_125)
        if not na(label_150)
            label.delete(label_150)

        bars_today = (bar_index + 1) - ta.barssince(timeframe.change('D'))
        one_bar_ahead = bar_index + (10 * (timeframe.multiplier / timeframe.multiplier)) + (1 * (timeframe.multiplier / timeframe.multiplier))
        ten_bars_ahead = bar_index + (20 * (timeframe.multiplier / timeframe.multiplier)) + (10 * (timeframe.multiplier / timeframe.multiplier))
        var int bars_ahead = 0

        if timeframe.period == '60'  // 1 hour
            bars_ahead := 25  // 24 bars for 1 day
        else if timeframe.period == '240'  // 4 hour
            bars_ahead := 7   // 6 bars for 1 day
        else if timeframe.period == '1'
            bars_ahead := 1441  // 1440 minutes in a day
        else
            bars_ahead := 1440 / timeframe.multiplier  // Adjust for any other timeframe

        
        // Draw the horizontal lines with labels
        line_261_8_neg := line.new(x1 = startIndex, y1 = level_261_8_neg, x2 = bar_index, y2 = level_261_8_neg, color = color.green, width = 3)//, extend = extend.right)

        line_200_neg := line.new(x1 = startIndex, y1 = level_200_neg, x2 = bar_index, y2 = level_200_neg, color = color.purple, width = 3)//, extend = extend.right)

        line_161_8_neg := line.new(x1 = startIndex, y1 = level_161_8_neg, x2 = bar_index, y2 = level_161_8_neg, color = color.green, width = 3)//, extend = extend.right)

        line_100_neg := line.new(x1 = startIndex, y1 = level_100_neg, x2 = bar_index, y2 = level_100_neg, color = color.green, width = 2)//, extend = extend.right)

        line_61_8_neg := line.new(x1 = startIndex, y1 = level_61_8_neg, x2 = bar_index, y2 = level_61_8_neg, color = color.green, width = 4)//, extend = extend.right)

        line_50_neg := line.new(x1 = startIndex, y1 = level_50_neg, x2 = bar_index, y2 = level_50_neg, color = color.red, width = 5)//, extend = extend.right)
        //label_50_neg := label.new(x = bar_index + 25, y = level_50_neg, text = '-50%', style = label.style_label_left, color = color.red, textcolor = color.white, size = size.small)

        line_25_neg := line.new(x1 = startIndex, y1 = level_25_neg, x2 = bar_index, y2 = level_25_neg, color = color.red, width = 3)//, extend = extend.right)
        //label_25_neg := label.new(x = bar_index + 25, y = level_25_neg, text = '-25%', style = label.style_label_left, color = color.red, textcolor = color.white, size = size.small)

        line_0 := line.new(x1 = startIndex, y1 = level_0, x2 = bar_index, y2 = level_0, color = color.navy, width = 3)//, extend = extend.right)

        line_25 := line.new(x1 = startIndex, y1 = level_25, x2 = bar_index, y2 = level_25, color = color.red, width = 2)//, extend = extend.right)
        //label_25 := label.new(x = bar_index + 25, y = level_25, text = '25%', style = label.style_label_left, color = color.red, textcolor = color.white, size = size.small)
        
        line_50 := line.new(x1 = startIndex, y1 = level_50, x2 = bar_index, y2 = level_50, color = color.navy, width = 5)//, extend = extend.right)
        //label_50 := label.new(x = bar_index + 25, y = level_50, text = '50%', style = label.style_label_left, color = color.red, textcolor = color.white, size = size.small)

        line_75 := line.new(x1 = startIndex, y1 = level_75, x2 = bar_index, y2 = level_75, color = color.red, width = 2)//, extend = extend.right)
        //label_75 := label.new(x = bar_index + 25, y = level_75, text = '75%', style = label.style_label_left, color = color.red, textcolor = color.white, size = size.small)

        line_100 := line.new(x1 = startIndex, y1 = level_100, x2 = bar_index, y2 = level_100, color = color.navy, width = 3)//, extend = extend.right)

        line_125 := line.new(x1 = startIndex, y1 = level_125, x2 = bar_index, y2 = level_125, color = color.red, width = 3)//, extend = extend.right)
        //label_125 := label.new(x = bar_index + 25, y = level_125, text = '125%', style = label.style_label_left, color = color.red, textcolor = color.white, size = size.small)

        line_150 := line.new(x1 = startIndex, y1 = level_150, x2 = bar_index, y2 = level_150, color = color.red, width = 5)//, extend = extend.right)
        //label_150 := label.new(x = bar_index + 25, y = level_150, text = '150%', style = label.style_label_left, color = color.red, textcolor = color.white, size = size.small)
        
        line_161_8 := line.new(x1 = startIndex, y1 = level_161_8, x2 = bar_index, y2 = level_161_8, color = color.green, width = 4)//, extend = extend.right)
        
        line_200 := line.new(x1 = startIndex, y1 = level_200, x2 = bar_index, y2 = level_200, color = color.green, width = 2)//, extend = extend.right)

        line_261_8 := line.new(x1 = startIndex, y1 = level_261_8, x2 = bar_index, y2 = level_261_8, color = color.green, width = 3)//, extend = extend.right)

        line_300 := line.new(x1 = startIndex, y1 = level_300, x2 = bar_index, y2 = level_300, color = color.purple, width = 3)//, extend = extend.right)

        line_361_8 := line.new(x1 = startIndex, y1 = level_361_8, x2 = bar_index, y2 = level_361_8, color = color.green, width = 3)//, extend = extend.right)
        
        // Draw yellow lines for .1 and .9 levels (without labels)
        line_55_9_neg := line.new(x1 = startIndex, y1 = level_55_9_neg, x2 = bar_index, y2 = level_55_9_neg, color = color.yellow, width = 1)//, extend = extend.right)
        line_44_1_neg := line.new(x1 = startIndex, y1 = level_44_1_neg, x2 = bar_index, y2 = level_44_1_neg, color = color.yellow, width = 1)//, extend = extend.right)
        line_30_9_neg := line.new(x1 = startIndex, y1 = level_30_9_neg, x2 = bar_index, y2 = level_30_9_neg, color = color.yellow, width = 1)//, extend = extend.right)
        line_19_1_neg := line.new(x1 = startIndex, y1 = level_19_1_neg, x2 = bar_index, y2 = level_19_1_neg, color = color.yellow, width = 1)//, extend = extend.right)
        line_5_9_neg := line.new(x1 = startIndex, y1 = level_5_9_neg, x2 = bar_index, y2 = level_5_9_neg, color = color.blue, width = 2)//, extend = extend.right)
        line_5_9 := line.new(x1 = startIndex, y1 = level_5_9, x2 = bar_index, y2 = level_5_9, color = color.yellow, width = 1)//, extend = extend.right)
        line_19_1 := line.new(x1 = startIndex, y1 = level_19_1, x2 = bar_index, y2 = level_19_1, color = color.yellow, width = 1)//, extend = extend.right)
        line_30_9 := line.new(x1 = startIndex, y1 = level_30_9, x2 = bar_index, y2 = level_30_9, color = color.yellow, width = 1)//, extend = extend.right)
        line_44_1 := line.new(x1 = startIndex, y1 = level_44_1, x2 = bar_index, y2 = level_44_1, color = color.blue, width = 2)//, extend = extend.right)
        line_55_9 := line.new(x1 = startIndex, y1 = level_55_9, x2 = bar_index, y2 = level_55_9, color = color.blue, width = 2)//, extend = extend.right)
        line_69_1 := line.new(x1 = startIndex, y1 = level_69_1, x2 = bar_index, y2 = level_69_1, color = color.yellow, width = 1)//, extend = extend.right)
        line_80_9 := line.new(x1 = startIndex, y1 = level_80_9, x2 = bar_index, y2 = level_80_9, color = color.yellow, width = 1)//, extend = extend.right)
        line_94_1 := line.new(x1 = startIndex, y1 = level_94_1, x2 = bar_index, y2 = level_94_1, color = color.yellow, width = 1)//, extend = extend.right)
        line_105_9 := line.new(x1 = startIndex, y1 = level_105_9, x2 = bar_index, y2 = level_105_9, color = color.blue, width = 2)//, extend = extend.right)
        line_119_1 := line.new(x1 = startIndex, y1 = level_119_1, x2 = bar_index, y2 = level_119_1, color = color.yellow, width = 1)//, extend = extend.right)
        line_130_9 := line.new(x1 = startIndex, y1 = level_130_9, x2 = bar_index, y2 = level_130_9, color = color.yellow, width = 1)//, extend = extend.right)
        line_144_1 := line.new(x1 = startIndex, y1 = level_144_1, x2 = bar_index, y2 = level_144_1, color = color.yellow, width = 1)//, extend = extend.right)
        line_155_9 := line.new(x1 = startIndex, y1 = level_155_9, x2 = bar_index, y2 = level_155_9, color = color.yellow, width = 1)//, extend = extend.right)

    // Reset variables for the new day
    dayHigh := na
    dayLow := na
    highIndex := na
    lowIndex := na
    lowIndex

    // Reset variables for the new day
    dayHigh1 := na
    dayLow1 := na
    highIndex1 := na
    lowIndex1 := na
    lowIndex1

// Update daily high and low values
isWithin10Hours = bar_index <= (dayStart10HourIndex + int(barsIn10Hours))

//lines fix - main
line.set_x1(line_261_8_neg, bar_index + 1)
line.set_x2(line_261_8_neg, bar_index + 15)
line.set_x1(line_200_neg, bar_index + 1)
line.set_x2(line_200_neg, bar_index + 15)
line.set_x1(line_161_8_neg, bar_index + 1)
line.set_x2(line_161_8_neg, bar_index + 15)
line.set_x1(line_100_neg, bar_index + 1)
line.set_x2(line_100_neg, bar_index + 15)
line.set_x1(line_61_8_neg, bar_index + 1)
line.set_x2(line_61_8_neg, bar_index + 15)
line.set_x1(line_50_neg, bar_index + 1)
line.set_x2(line_50_neg, bar_index + 15)
line.set_x1(line_25_neg, bar_index + 1)
line.set_x2(line_25_neg, bar_index + 15)
line.set_x1(line_0, bar_index + 1)
line.set_x2(line_0, bar_index + 15)
line.set_x1(line_25, bar_index + 1)
line.set_x2(line_25, bar_index + 15)
line.set_x1(line_50, bar_index + 1)
line.set_x2(line_50, bar_index + 15)
line.set_x1(line_75, bar_index + 1)
line.set_x2(line_75, bar_index + 15)
line.set_x1(line_100, bar_index + 1)
line.set_x2(line_100, bar_index + 15)
line.set_x1(line_125, bar_index + 1)
line.set_x2(line_125, bar_index + 15)
line.set_x1(line_150, bar_index + 1)
line.set_x2(line_150, bar_index + 15)
line.set_x1(line_161_8, bar_index + 1)
line.set_x2(line_161_8, bar_index + 15)
line.set_x1(line_200, bar_index + 1)
line.set_x2(line_200, bar_index + 15)
line.set_x1(line_261_8, bar_index + 1)
line.set_x2(line_261_8, bar_index + 15)
line.set_x1(line_300, bar_index + 1)
line.set_x2(line_300, bar_index + 15)
line.set_x1(line_361_8, bar_index + 1)
line.set_x2(line_361_8, bar_index + 15)

//lines fix - secondary
line.set_x1(line_5_9_neg, bar_index + 1)
line.set_x2(line_5_9_neg, bar_index + 13)
line.set_x1(line_44_1, bar_index + 1)
line.set_x2(line_44_1, bar_index + 13)
line.set_x1(line_55_9, bar_index + 1)
line.set_x2(line_55_9, bar_index + 13)
line.set_x1(line_105_9, bar_index + 1)
line.set_x2(line_105_9, bar_index + 13)

//lines fix - minor
line.set_x1(line_55_9_neg, bar_index + 1)
line.set_x2(line_55_9_neg, bar_index + 10)
line.set_x1(line_44_1_neg, bar_index + 1)
line.set_x2(line_44_1_neg, bar_index + 10)
line.set_x1(line_30_9_neg, bar_index + 1)
line.set_x2(line_30_9_neg, bar_index + 10)
line.set_x1(line_19_1_neg, bar_index + 1)
line.set_x2(line_19_1_neg, bar_index + 10)
line.set_x1(line_5_9, bar_index + 1)
line.set_x2(line_5_9, bar_index + 10)
line.set_x1(line_19_1, bar_index + 1)
line.set_x2(line_19_1, bar_index + 10)
line.set_x1(line_30_9, bar_index + 1)
line.set_x2(line_30_9, bar_index + 10)
line.set_x1(line_69_1, bar_index + 1)
line.set_x2(line_69_1, bar_index + 10)
line.set_x1(line_80_9, bar_index + 1)
line.set_x2(line_80_9, bar_index + 10)
line.set_x1(line_94_1, bar_index + 1)
line.set_x2(line_94_1, bar_index + 10)
line.set_x1(line_119_1, bar_index + 1)
line.set_x2(line_119_1, bar_index + 10)
line.set_x1(line_130_9, bar_index + 1)
line.set_x2(line_130_9, bar_index + 10)
line.set_x1(line_144_1, bar_index + 1)
line.set_x2(line_144_1, bar_index + 10)
line.set_x1(line_155_9, bar_index + 1)
line.set_x2(line_155_9, bar_index + 10)


if isWithin10Hours
    if na(dayHigh) or high > dayHigh
        dayHigh := high
        highIndex := bar_index
    if na(dayLow) or low < dayLow
        dayLow := low
        lowIndex := bar_index

// Update daily high and low values
if na(dayHigh1) or high > dayHigh1
    dayHigh1 := high
    highIndex1 := bar_index
    highIndex1
if na(dayLow1) or low < dayLow1
    dayLow1 := low
    lowIndex1 := bar_index
    lowIndex1

// After 10 hour period completes
if bar_index == (dayStart10HourIndex + int(barsIn10Hours))
    if not na(dayHigh) and not na(dayLow)
        // Delete previous lines if they exist
        line.delete(highLine)
        line.delete(midLine)
        line.delete(lowLine)
        // Delete previous mid line
        //line.delete(tenHourMidLine)
        
        midPrice = dayLow + ((dayHigh - dayLow) / 2)
        
        // Draw new lines and store their IDs
        highLine := line.new(x1=dayStartIndex, y1=dayHigh, x2=dayEndIndex, y2=dayHigh, color=color.yellow, width=3)
        midLine := line.new(x1=dayStartIndex, y1=midPrice, x2=dayEndIndex, y2=midPrice, color=color.yellow, width=3)
        lowLine := line.new(x1=dayStartIndex, y1=dayLow, x2=dayEndIndex, y2=dayLow, color=color.yellow, width=3)
        // Draw new mid line
        
        if array.size(tenHourMidLine) >= maxHistoryDays
            line.delete(array.shift(tenHourMidLine))
        
        //thourmid = line.new(x1=dayStartIndex, y1=midPrice, x2=dayEndIndex, y2=midPrice, color=color.purple, width=2)

        //array.push(tenHourMidLine, thourmid)

        // Delete oldest box if we have 10 days
        if array.size(tenHourBoxes) >= maxHistoryDays
            box.delete(array.shift(tenHourBoxes))
            
        // Create new 10-hour box
        //newBox = box.new(left=dayStartIndex, right=dayStart10HourIndex + int(barsIn10Hours), top=dayHigh, bottom=dayLow, bgcolor=color.new(color.aqua, 90), border_color=color.new(color.aqua, 70), border_width=1)
            
        //array.push(tenHourBoxes, newBox)

        // Calculate 50% extensions
        range1 = dayHigh - dayLow
        upperExt = dayHigh + (range1 * 0.5)
        lowerExt = dayLow - (range1 * 0.5)

        // Delete previous extension boxes
        box.delete(upperExtBox)
        box.delete(lowerExtBox)

        // Draw extension boxes
        //upperExtBox := box.new(left=dayStartIndex, right=dayEndIndex, top=upperExt, bottom=dayHigh, bgcolor=color.new(color.blue, 90), border_color=color.blue, border_width=2)

        //lowerExtBox := box.new(left=dayStartIndex, right=dayEndIndex, top=dayLow, bottom=lowerExt, bgcolor=color.new(color.red, 90), border_color=color.red, border_width=2)
