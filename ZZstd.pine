//@version=6
indicator("ZigZag StdDev Rectangles", shorttitle="ZZstd", overlay=true, max_boxes_count=500, max_lines_count=500, max_bars_back=5000)

// Input parameters
zigzag_depth = input.int(8, "ZigZag Depth", minval=1)
zigzag_deviation = input.int(5, "ZigZag Deviation", minval=1)
zigzag_backstep = input.int(3, "ZigZag Backstep", minval=1)
swing_points_to_analyze = input.int(30, "Swing Points to Analyze", minval=5, maxval=100)
normal_std = input.bool(true, "Normal Standard Deviation")
std_dev_multiplier1 = input.float(1.0, "StdDev Multiplier 1", minval=0.1, maxval=5.0)
std_dev_multiplier2 = input.float(2.0, "StdDev Multiplier 2", minval=0.1, maxval=5.0)
high_rect_color = input.color(color.new(color.green, 80), "High Rectangle Color")
low_rect_color = input.color(color.new(color.fuchsia, 80), "Low Rectangle Color")
swing_line_color = input.color(color.yellow, "Swing Line Color")

// Adjust multipliers based on normal_std setting
final_multiplier1 = normal_std ? 1.0 : 1.229
final_multiplier2 = normal_std ? 2.0 : 1.771

// ZigZag calculation
var float[] zigzag_highs = array.new<float>()
var int[] zigzag_high_bars = array.new<int>()
var float[] zigzag_lows = array.new<float>()
var int[] zigzag_low_bars = array.new<int>()

// Arrays to store drawing objects for deletion
var box[] all_boxes = array.new<box>()
var line[] all_lines = array.new<line>()

// Function to calculate standard deviation
calculate_std_dev(start_bar, end_bar) =>
    if start_bar == end_bar
        0.0
    else
        count = math.abs(start_bar - end_bar)
        if count < 2
            0.0
        else
            sum = 0.0
            sum_sq = 0.0
            valid = 0
            
            min_bar = math.min(start_bar, end_bar)
            max_bar = math.max(start_bar, end_bar)
            
            for i = min_bar to max_bar
                bars_back = bar_index - i
                if i >= 0 and i < bar_index and bars_back >= 0 and bars_back <= 5000
                    mid = (high[bars_back] + low[bars_back]) / 2
                    if mid > 0
                        sum += mid
                        sum_sq += mid * mid
                        valid += 1
            
            if valid < 2
                0.0
            else
                mean = sum / valid
                variance = (sum_sq / valid) - (mean * mean)
                math.sqrt(math.abs(variance))

// ZigZag detection function
detect_zigzag() =>
    var float last_high = na
    var int last_high_bar = na
    var float last_low = na
    var int last_low_bar = na
    var int trend = 0  // 1 for up, -1 for down
    
    current_high = high
    current_low = low
    current_bar = bar_index
    
    // Look for potential highs - check if current high is higher than previous bars
    is_high = bar_index >= zigzag_depth
    if is_high
        for i = 1 to zigzag_depth
            if high[i] >= current_high
                is_high := false
                break
    
    // Look for potential lows - check if current low is lower than previous bars
    is_low = bar_index >= zigzag_depth
    if is_low
        for i = 1 to zigzag_depth
            if low[i] <= current_low
                is_low := false
                break
    
    // Process zigzag points
    if is_high and (na(last_high) or current_high > last_high + zigzag_deviation * syminfo.mintick)
        if trend != 1
            // Store the low before this high
            if not na(last_low)
                if array.size(zigzag_lows) >= swing_points_to_analyze
                    array.shift(zigzag_lows)
                    array.shift(zigzag_low_bars)
                array.unshift(zigzag_lows, last_low)
                array.unshift(zigzag_low_bars, last_low_bar)
            trend := 1
        
        last_high := current_high
        last_high_bar := current_bar
        
        // Store this high
        if array.size(zigzag_highs) >= swing_points_to_analyze
            array.shift(zigzag_highs)
            array.shift(zigzag_high_bars)
        array.unshift(zigzag_highs, current_high)
        array.unshift(zigzag_high_bars, current_bar)
    
    if is_low and (na(last_low) or current_low < last_low - zigzag_deviation * syminfo.mintick)
        if trend != -1
            // Store the high before this low
            if not na(last_high)
                if array.size(zigzag_highs) >= swing_points_to_analyze
                    array.shift(zigzag_highs)
                    array.shift(zigzag_high_bars)
                array.unshift(zigzag_highs, last_high)
                array.unshift(zigzag_high_bars, last_high_bar)
            trend := -1
        
        last_low := current_low
        last_low_bar := current_bar
        
        // Store this low
        if array.size(zigzag_lows) >= swing_points_to_analyze
            array.shift(zigzag_lows)
            array.shift(zigzag_low_bars)
        array.unshift(zigzag_lows, current_low)
        array.unshift(zigzag_low_bars, current_bar)

// Main execution - run on every bar to build zigzag data
detect_zigzag()

if barstate.islast
    
    // Clear previous drawings
    if array.size(all_boxes) > 0
        for i = 0 to array.size(all_boxes) - 1
            box.delete(id=array.get(all_boxes, i))
    array.clear(all_boxes)
    
    if array.size(all_lines) > 0
        for i = 0 to array.size(all_lines) - 1
            line.delete(id=array.get(all_lines, i))
    array.clear(all_lines)
    
    // Process high rectangles
    if array.size(zigzag_highs) > 1
        for i = 1 to math.min(array.size(zigzag_highs) - 1, swing_points_to_analyze - 1)
            curr_high = array.get(zigzag_highs, i)
            curr_bar = array.get(zigzag_high_bars, i)
            prev_bar = i > 0 ? array.get(zigzag_high_bars, i - 1) : curr_bar
            
            std_dev = calculate_std_dev(curr_bar, prev_bar)
            
            top = curr_high + std_dev * final_multiplier2
            bottom = curr_high + std_dev * final_multiplier1
            
            // Check if level was hit
            was_hit = false
            hit_bar = bar_index
            
            for j = curr_bar + 1 to bar_index
                bars_back = bar_index - j
                if j < bar_index and bars_back >= 0 and bars_back <= 5000 and high[bars_back] >= bottom
                    was_hit := true
                    hit_bar := j
                    break
            
            start_time = math.max(curr_bar, prev_bar)
            end_time = was_hit ? hit_bar : bar_index
            
            // Create rectangle - use proper time coordinates
            rect_style = was_hit ? line.style_dashed : line.style_solid
            
            // Use time coordinates directly
            start_bars_back = bar_index - start_time
            end_bars_back = bar_index - end_time
            
            // Ensure we don't go beyond available history
            start_bars_back := math.max(0, math.min(start_bars_back, 5000))
            end_bars_back := math.max(0, math.min(end_bars_back, 5000))
            
            left_time = time[start_bars_back]
            right_time = time[end_bars_back]
            
            new_box = box.new(left_time, top, right_time, bottom,
                   border_color=high_rect_color, bgcolor=color.new(high_rect_color, 90),
                   border_style=rect_style, xloc=xloc.bar_time)
            array.push(all_boxes, new_box)
    
    // Process low rectangles
    if array.size(zigzag_lows) > 1
        for i = 1 to math.min(array.size(zigzag_lows) - 1, swing_points_to_analyze - 1)
            curr_low = array.get(zigzag_lows, i)
            curr_bar = array.get(zigzag_low_bars, i)
            prev_bar = i > 0 ? array.get(zigzag_low_bars, i - 1) : curr_bar
            
            std_dev = calculate_std_dev(curr_bar, prev_bar)
            
            top = curr_low - std_dev * final_multiplier1
            bottom = curr_low - std_dev * final_multiplier2
            
            // Check if level was hit
            was_hit = false
            hit_bar = bar_index
            
            for j = curr_bar + 1 to bar_index
                bars_back = bar_index - j
                if j < bar_index and bars_back >= 0 and bars_back <= 5000 and low[bars_back] <= top
                    was_hit := true
                    hit_bar := j
                    break
            
            start_time = math.max(curr_bar, prev_bar)
            end_time = was_hit ? hit_bar : bar_index
            
            // Create rectangle - use proper time coordinates
            rect_style = was_hit ? line.style_dashed : line.style_solid
            
            // Use time coordinates directly
            start_bars_back = bar_index - start_time
            end_bars_back = bar_index - end_time
            
            // Ensure we don't go beyond available history
            start_bars_back := math.max(0, math.min(start_bars_back, 5000))
            end_bars_back := math.max(0, math.min(end_bars_back, 5000))
            
            left_time = time[start_bars_back]
            right_time = time[end_bars_back]
            
            new_box = box.new(left_time, top, right_time, bottom,
                   border_color=low_rect_color, bgcolor=color.new(low_rect_color, 90),
                   border_style=rect_style, xloc=xloc.bar_time)
            array.push(all_boxes, new_box)
    
    // Create combined array for retracement levels
    var combined_points = array.new<float>()
    var combined_bars = array.new<int>()
    var combined_is_high = array.new<bool>()
    
    array.clear(combined_points)
    array.clear(combined_bars)
    array.clear(combined_is_high)
    
    // Add all highs
    if array.size(zigzag_highs) > 0
        for i = 0 to array.size(zigzag_highs) - 1
            array.push(combined_points, array.get(zigzag_highs, i))
            array.push(combined_bars, array.get(zigzag_high_bars, i))
            array.push(combined_is_high, true)
    
    // Add all lows
    if array.size(zigzag_lows) > 0
        for i = 0 to array.size(zigzag_lows) - 1
            array.push(combined_points, array.get(zigzag_lows, i))
            array.push(combined_bars, array.get(zigzag_low_bars, i))
            array.push(combined_is_high, false)
    
    // Sort by bar index (bubble sort)
    size = array.size(combined_points)
    if size > 1
        for i = 0 to size - 2
            for j = i + 1 to size - 1
                if array.get(combined_bars, i) > array.get(combined_bars, j)
                    // Swap elements
                    temp_price = array.get(combined_points, i)
                    temp_bar = array.get(combined_bars, i)
                    temp_is_high = array.get(combined_is_high, i)
                    
                    array.set(combined_points, i, array.get(combined_points, j))
                    array.set(combined_bars, i, array.get(combined_bars, j))
                    array.set(combined_is_high, i, array.get(combined_is_high, j))
                    
                    array.set(combined_points, j, temp_price)
                    array.set(combined_bars, j, temp_bar)
                    array.set(combined_is_high, j, temp_is_high)
    
    // Draw retracement levels
    if array.size(combined_points) > 1
        for i = 0 to array.size(combined_points) - 2
            start_price = array.get(combined_points, i)
            end_price = array.get(combined_points, i + 1)
            start_bar = array.get(combined_bars, i)
            end_bar = array.get(combined_bars, i + 1)
            is_upward = not array.get(combined_is_high, i) and array.get(combined_is_high, i + 1)
            
            swing_range = end_price - start_price
            if swing_range != 0
                level_229 = is_upward ? start_price + swing_range * 0.229 : start_price + swing_range * 0.771
                level_771 = is_upward ? start_price + swing_range * 0.771 : start_price + swing_range * 0.229
                
                mid_bar = math.round((start_bar + end_bar) / 2)
                
                // Check if levels are hit
                hit_229 = false
                hit_771 = false
                hit_bar_229 = bar_index
                hit_bar_771 = bar_index
                
                for j = mid_bar + 1 to bar_index
                    bars_back = bar_index - j
                    if j < bar_index and bars_back >= 0 and bars_back <= 5000
                        if not hit_229
                            if is_upward ? high[bars_back] >= level_229 : low[bars_back] <= level_229
                                hit_229 := true
                                hit_bar_229 := j
                        
                        if not hit_771
                            if is_upward ? high[bars_back] >= level_771 : low[bars_back] <= level_771
                                hit_771 := true
                                hit_bar_771 := j
                
                // Draw 22.9% level
                start_bars_back_229 = math.max(0, math.min(bar_index - mid_bar, 5000))
                end_bars_back_229 = math.max(0, math.min(bar_index - hit_bar_229, 5000))
                line_start_time_229 = time[start_bars_back_229]
                line_end_time_229 = time[end_bars_back_229]
                new_line_229 = line.new(line_start_time_229, level_229, line_end_time_229, level_229, color=swing_line_color, width=hit_229 ? 3 : 1, style=hit_229 ? line.style_solid : line.style_dashed, xloc=xloc.bar_time)
                array.push(all_lines, new_line_229)
                
                // Draw 77.1% level
                start_bars_back_771 = math.max(0, math.min(bar_index - mid_bar, 5000))
                end_bars_back_771 = math.max(0, math.min(bar_index - hit_bar_771, 5000))
                line_start_time_771 = time[start_bars_back_771]
                line_end_time_771 = time[end_bars_back_771]
                new_line_771 = line.new(line_start_time_771, level_771, line_end_time_771, level_771, color=swing_line_color, width=hit_771 ? 3 : 1, style=hit_771 ? line.style_solid : line.style_dashed, xloc=xloc.bar_time)
                array.push(all_lines, new_line_771)

// Plot ZigZag points for visualization
plot_high = array.size(zigzag_highs) > 0 and array.size(zigzag_high_bars) > 0 and bar_index == array.get(zigzag_high_bars, 0) ? array.get(zigzag_highs, 0) : na
plot_low = array.size(zigzag_lows) > 0 and array.size(zigzag_low_bars) > 0 and bar_index == array.get(zigzag_low_bars, 0) ? array.get(zigzag_lows, 0) : na

plotshape(plot_high, style=shape.triangledown, location=location.abovebar, color=color.blue, size=size.small, title="ZigZag High")
plotshape(plot_low, style=shape.triangleup, location=location.belowbar, color=color.red, size=size.small, title="ZigZag Low")