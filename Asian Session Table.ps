//@version=6
indicator("Multi-Pair Asian Session Signal Table", overlay=true)

default = 600

effective_minutas = input.int(default, "Minutes for Asia session",
     options=[539, 599, 779, 989, default])

// Get UTC time from TradingView
utcHour = hour(timenow)
utcMinute = minute(timenow)

// New York Time = UTC-5 (standard) or UTC-4 (DST)
isDST = month >= 3 and month <= 11 and (month > 3 or (month == 3 and dayofmonth >= 8)) and (month < 11 or (month == 11 and dayofmonth < 7))
nyOffset = isDST ? -4 : -5  // NY is UTC-4 in summer, UTC-5 in winter

// Convert New York time to UTC+2
utcPlus2Offset = 2 - nyOffset
//currentHour = (utcHour + utcPlus2Offset) % 24
currentHour = hour(timenow) + 7
currentMinute = utcMinute

if currentHour >= 0 and currentHour < 10
    effective_minutas := 539
if currentHour >= 10 and currentHour < 13
    effective_minutas := 599
if currentHour >= 13 and (currentHour < 16 or (currentHour == 16 and currentMinute < 30))
    effective_minutas := 779
if (currentHour > 16 or (currentHour >= 16 and currentMinute >= 30)) and currentHour < 24
    effective_minutas := 989

// Add variables for 10-hour tracking
var float MINUTES_IN_10_HOURS = effective_minutas  // 10 hours * 60 minutes
var float barsIn10Hours = MINUTES_IN_10_HOURS / timeframe.multiplier
var int dayStart10HourIndex = na

lookbackPeriod = 120
newDay = ta.change(time('D'))

if bool(newDay)
    dayStart10HourIndex := bar_index

// Update daily high and low values
isWithin10Hours = bar_index <= (dayStart10HourIndex + int(barsIn10Hours))

// Function to calculate the signal
f_calcSessionHL(lookbackPeriod) =>
    // persistent session high and low that reset each day
    var float sessHigh = na
    var float sessLow  = na
    var float sOpen    = na
    if ta.change(time("D")) != 0
        sessHigh := na
        sessLow  := na
        sOpen    := na
    // Only update during 00:00 to 09:59 (hour < 10)
    if isWithin10Hours
        sOpen    := na(sOpen) ? open : sOpen
        sessHigh := na(sessHigh) ? high : math.max(sessHigh, high)
        sessLow  := na(sessLow)  ? low  : math.min(sessLow, low)
    // Lookback calculations
    highLB   = ta.highest(high, lookbackPeriod)[6]
    lowLB    = ta.lowest(low, lookbackPeriod)[6]
    dist     = highLB - lowLB
    lowdist  = lowLB + 0.236 * dist
    higdist  = highLB - 0.236 * dist
    middist  = lowLB + (dist / 2)
    // New ATR and moving average calculations
    atr21      = ta.atr(21)
    sqrtFactor = math.sqrt(21 * 0.5 * 0.5)
    mixa       = open + sqrtFactor * atr21
    mixb       = open - sqrtFactor * atr21
    lrh        = ta.linreg(mixa, 72, 0)
    lrl        = ta.linreg(mixb, 72, 0)
    [sessHigh, sessLow, sOpen, highLB, lowLB, dist, lowdist, higdist, middist, lrh, lrl]

// Wrap the session values and close in a single function
f_calcSessionData(lookbackPeriod) =>
    [sessHigh, sessLow, sOpen, highLB, lowLB, dist, lowdist, higdist, middist, lrh, lrl] = f_calcSessionHL(lookbackPeriod)
    [sessHigh, sessLow, close, sOpen, syminfo.mintick, highLB, lowLB, dist, lowdist, higdist, middist, lrh, lrl]

// Function to fetch security values and calculate midline and zone
f_getZone(symbol, tf) =>
    // Get the session high/low via the helper function for the given security
    [sessHigh, sessLow, sClose, sOpen, mintick, highLB, lowLB, dist, lowdist, higdist, middist, lrh, lrl] = request.security(symbol, tf, f_calcSessionData(lookbackPeriod), lookahead=barmerge.lookahead_on)
    // Calculate its midline
    sMid = (sessHigh + sessLow) / 2
    midRange = (sessHigh - sessLow) / 2
    // Determine the zone
    zone = sClose >= sessHigh + midRange ? 6 :
           sClose >= sessHigh and sClose < sessHigh + midRange ? 5 :
           sClose >= sMid and sClose < sessHigh ? 4 :
           sClose > sessLow and sClose < sMid  ? 2 :
           sClose <= sessLow and sClose > sessLow - midRange ? 1 :
           sClose <= sessLow - midRange ? 0 : 3
    // Get deezcrap zone
    deezcrap = sClose >= highLB ? 6 :
               sClose < highLB and sClose >= higdist ? 5 :
               sClose < higdist and sClose >= middist ? 4 :
               sClose <= middist and sClose > lowdist ? 2 :
               sClose <= lowdist and sClose > lowLB ? 1 :
               sClose <= lowLB ? 0 : 3
    // Get location vs mixa and mixb
    tryk = sClose > lrh ? 2 :
          sClose > lrl and sClose < lrh ? 1 :
          sClose < lrl ? 0 : 3
    // Get the minimal tick from the security to estimate decimals
    //mintick = request.security(symbol, tf, syminfo.mintick, lookahead=barmerge.lookahead_on)
    decimals = math.round(math.abs(math.log(mintick) / math.log(10)))
    multiplier = decimals == 3 ? 100 : 10000
    bool isBroken = false
    float diff = na
    if sClose > sessHigh
        diff := (sClose - sessHigh) * multiplier
        isBroken := true
    else if sClose < sessLow
        diff := (sClose - sessLow) * multiplier
        isBroken := true
    else
        diff := (sClose - sMid) * multiplier
        isBroken := false
    //diff = (sClose - sMid) * multiplier
    drang = (sessHigh - sessLow) * multiplier
    //dang = (sClose - sMid) * multiplier
    pips = (sClose - sOpen) * multiplier
    [zone, sMid, diff, drang, isBroken, pips, deezcrap, tryk]

// Symbols for analysis
symbol0 = input.symbol("FX:EURUSD", title="Symbol 1")
symbol1 = input.symbol("FX:EURGBP", title="Symbol 2")
symbol2 = input.symbol("FX:EURAUD", title="Symbol 3")
symbol3 = input.symbol("FX:EURNZD", title="Symbol 4")
symbol4 = input.symbol("FX:EURCAD", title="Symbol 5")
symbol5 = input.symbol("FX:EURCHF", title="Symbol 6")
symbol6 = input.symbol("FX:EURJPY", title="Symbol 7")
symbol7 = input.symbol("FX:GBPUSD", title="Symbol 8")
symbol8 = input.symbol("FX:GBPAUD", title="Symbol 9")
symbol9 = input.symbol("FX:GBPNZD", title="Symbol 10")
symbol10 = input.symbol("FX:GBPCAD", title="Symbol 11")
symbol11 = input.symbol("FX:GBPCHF", title="Symbol 12")
symbol12 = input.symbol("FX:GBPJPY", title="Symbol 13")
symbol13 = input.symbol("FX:AUDUSD", title="Symbol 14")
symbol14 = input.symbol("FX:AUDNZD", title="Symbol 15")
symbol15 = input.symbol("FX:AUDCAD", title="Symbol 16")
symbol16 = input.symbol("FX:AUDCHF", title="Symbol 17")
symbol17 = input.symbol("FX:AUDJPY", title="Symbol 18")
symbol18 = input.symbol("FX:NZDUSD", title="Symbol 19")
symbol19 = input.symbol("FX:NZDCAD", title="Symbol 20")
symbol20 = input.symbol("FX:NZDCHF", title="Symbol 21")
symbol21 = input.symbol("FX:NZDJPY", title="Symbol 22")
symbol22 = input.symbol("FX:USDCAD", title="Symbol 23")
symbol23 = input.symbol("FX:USDCHF", title="Symbol 24")
symbol24 = input.symbol("FX:USDJPY", title="Symbol 25")
symbol25 = input.symbol("FX:CADCHF", title="Symbol 26")
symbol26 = input.symbol("FX:CADJPY", title="Symbol 27")
symbol27 = input.symbol("FX:CHFJPY", title="Symbol 28")

// Fetch zone and midline for each symbol using the 1h timeframe
[zone0,  mid0, diff0, drang0, br0, pips0, deezcrap0, tryk0]  = f_getZone(symbol0,  "60")
[zone1,  mid1, diff1, drang1, br1, pips1, deezcrap1, tryk1]  = f_getZone(symbol1,  "60")
[zone2,  mid2, diff2, drang2, br2, pips2, deezcrap2, tryk2]  = f_getZone(symbol2,  "60")
[zone3,  mid3, diff3, drang3, br3, pips3, deezcrap3, tryk3]  = f_getZone(symbol3,  "60")
[zone4,  mid4, diff4, drang4, br4, pips4, deezcrap4, tryk4]  = f_getZone(symbol4,  "60")
[zone5,  mid5, diff5, drang5, br5, pips5, deezcrap5, tryk5]  = f_getZone(symbol5,  "60")
[zone6,  mid6, diff6, drang6, br6, pips6, deezcrap6, tryk6]  = f_getZone(symbol6,  "60")
[zone7,  mid7, diff7, drang7, br7, pips7, deezcrap7, tryk7]  = f_getZone(symbol7,  "60")
[zone8,  mid8, diff8, drang8, br8, pips8, deezcrap8, tryk8]  = f_getZone(symbol8,  "60")
[zone9,  mid9, diff9, drang9, br9, pips9, deezcrap9, tryk9]  = f_getZone(symbol9,  "60")
[zone10, mid10, diff10, drang10, br10, pips10, deezcrap10, tryk10] = f_getZone(symbol10, "60")
[zone11, mid11, diff11, drang11, br11, pips11, deezcrap11, tryk11] = f_getZone(symbol11, "60")
[zone12, mid12, diff12, drang12, br12, pips12, deezcrap12, tryk12] = f_getZone(symbol12, "60")
[zone13, mid13, diff13, drang13, br13, pips13, deezcrap13, tryk13] = f_getZone(symbol13, "60")
[zone14, mid14, diff14, drang14, br14, pips14, deezcrap14, tryk14] = f_getZone(symbol14, "60")
[zone15, mid15, diff15, drang15, br15, pips15, deezcrap15, tryk15] = f_getZone(symbol15, "60")
[zone16, mid16, diff16, drang16, br16, pips16, deezcrap16, tryk16] = f_getZone(symbol16, "60")
[zone17, mid17, diff17, drang17, br17, pips17, deezcrap17, tryk17] = f_getZone(symbol17, "60")
[zone18, mid18, diff18, drang18, br18, pips18, deezcrap18, tryk18] = f_getZone(symbol18, "60")
[zone19, mid19, diff19, drang19, br19, pips19, deezcrap19, tryk19] = f_getZone(symbol19, "60")
[zone20, mid20, diff20, drang20, br20, pips20, deezcrap20, tryk20] = f_getZone(symbol20, "60")
[zone21, mid21, diff21, drang21, br21, pips21, deezcrap21, tryk21] = f_getZone(symbol21, "60")
[zone22, mid22, diff22, drang22, br22, pips22, deezcrap22, tryk22] = f_getZone(symbol22, "60")
[zone23, mid23, diff23, drang23, br23, pips23, deezcrap23, tryk23] = f_getZone(symbol23, "60")
[zone24, mid24, diff24, drang24, br24, pips24, deezcrap24, tryk24] = f_getZone(symbol24, "60")
[zone25, mid25, diff25, drang25, br25, pips25, deezcrap25, tryk25] = f_getZone(symbol25, "60")
[zone26, mid26, diff26, drang26, br26, pips26, deezcrap26, tryk26] = f_getZone(symbol26, "60")
[zone27, mid27, diff27, drang27, br27, pips27, deezcrap27, tryk27] = f_getZone(symbol27, "60")

// After table population inside the barstate.islast block:
var float totalDiff   = 0.0
var float totalDrang  = 0.0

totalDiff  := ( (br0 ) ? math.abs(diff0)  : 0 ) +
              ( (br1 ) ? math.abs(diff1)  : 0 ) +
              ( (br2 ) ? math.abs(diff2)  : 0 ) +
              ( (br3 ) ? math.abs(diff3)  : 0 ) +
              ( (br4 ) ? math.abs(diff4)  : 0 ) +
              ( (br5 ) ? math.abs(diff5)  : 0 ) +
              ( (br6 ) ? math.abs(diff6)  : 0 ) +
              ( (br7 ) ? math.abs(diff7)  : 0 ) +
              ( (br8 ) ? math.abs(diff8)  : 0 ) +
              ( (br9 ) ? math.abs(diff9)  : 0 ) +
              ( (br10) ? math.abs(diff10) : 0 ) +
              ( (br11) ? math.abs(diff11) : 0 ) +
              ( (br12) ? math.abs(diff12) : 0 ) +
              ( (br13) ? math.abs(diff13) : 0 ) +
              ( (br14) ? math.abs(diff14) : 0 ) +
              ( (br15) ? math.abs(diff15) : 0 ) +
              ( (br16) ? math.abs(diff16) : 0 ) +
              ( (br17) ? math.abs(diff17) : 0 ) +
              ( (br18) ? math.abs(diff18) : 0 ) +
              ( (br19) ? math.abs(diff19) : 0 ) +
              ( (br20) ? math.abs(diff20) : 0 ) +
              ( (br21) ? math.abs(diff21) : 0 ) +
              ( (br22) ? math.abs(diff22) : 0 ) +
              ( (br23) ? math.abs(diff23) : 0 ) +
              ( (br24) ? math.abs(diff24) : 0 ) +
              ( (br25) ? math.abs(diff25) : 0 ) +
              ( (br26) ? math.abs(diff26) : 0 ) +
              ( (br27) ? math.abs(diff27) : 0 )

totalDrang := ( (br0 ) ? drang0  : 0 ) +
              ( (br1 ) ? drang1  : 0 ) +
              ( (br2 ) ? drang2  : 0 ) +
              ( (br3 ) ? drang3  : 0 ) +
              ( (br4 ) ? drang4  : 0 ) +
              ( (br5 ) ? drang5  : 0 ) +
              ( (br6 ) ? drang6  : 0 ) +
              ( (br7 ) ? drang7  : 0 ) +
              ( (br8 ) ? drang8  : 0 ) +
              ( (br9 ) ? drang9  : 0 ) +
              ( (br10) ? drang10 : 0 ) +
              ( (br11) ? drang11 : 0 ) +
              ( (br12) ? drang12 : 0 ) +
              ( (br13) ? drang13 : 0 ) +
              ( (br14) ? drang14 : 0 ) +
              ( (br15) ? drang15 : 0 ) +
              ( (br16) ? drang16 : 0 ) +
              ( (br17) ? drang17 : 0 ) +
              ( (br18) ? drang18 : 0 ) +
              ( (br19) ? drang19 : 0 ) +
              ( (br20) ? drang20 : 0 ) +
              ( (br21) ? drang21 : 0 ) +
              ( (br22) ? drang22 : 0 ) +
              ( (br23) ? drang23 : 0 ) +
              ( (br24) ? drang24 : 0 ) +
              ( (br25) ? drang25 : 0 ) +
              ( (br26) ? drang26 : 0 ) +
              ( (br27) ? drang27 : 0 )

// Create a new ratio calculation using ALL symbol diffs (absolute) and all drang values
var float totalAbsDiff = 0.0
var float totalDrangAll = 0.0

totalAbsDiff := ( (not br0 ) ? math.abs(diff0)  : 0 ) +
              ( (not br1 ) ? math.abs(diff1)  : 0 ) +
              ( (not br2 ) ? math.abs(diff2)  : 0 ) +
              ( (not br3 ) ? math.abs(diff3)  : 0 ) +
              ( (not br4 ) ? math.abs(diff4)  : 0 ) +
              ( (not br5 ) ? math.abs(diff5)  : 0 ) +
              ( (not br6 ) ? math.abs(diff6)  : 0 ) +
              ( (not br7 ) ? math.abs(diff7)  : 0 ) +
              ( (not br8 ) ? math.abs(diff8)  : 0 ) +
              ( (not br9 ) ? math.abs(diff9)  : 0 ) +
              ( (not br10) ? math.abs(diff10) : 0 ) +
              ( (not br11) ? math.abs(diff11) : 0 ) +
              ( (not br12) ? math.abs(diff12) : 0 ) +
              ( (not br13) ? math.abs(diff13) : 0 ) +
              ( (not br14) ? math.abs(diff14) : 0 ) +
              ( (not br15) ? math.abs(diff15) : 0 ) +
              ( (not br16) ? math.abs(diff16) : 0 ) +
              ( (not br17) ? math.abs(diff17) : 0 ) +
              ( (not br18) ? math.abs(diff18) : 0 ) +
              ( (not br19) ? math.abs(diff19) : 0 ) +
              ( (not br20) ? math.abs(diff20) : 0 ) +
              ( (not br21) ? math.abs(diff21) : 0 ) +
              ( (not br22) ? math.abs(diff22) : 0 ) +
              ( (not br23) ? math.abs(diff23) : 0 ) +
              ( (not br24) ? math.abs(diff24) : 0 ) +
              ( (not br25) ? math.abs(diff25) : 0 ) +
              ( (not br26) ? math.abs(diff26) : 0 ) +
              ( (not br27) ? math.abs(diff27) : 0 )

totalDrangAll := ( (not br0 ) ? drang0  : 0 ) +
              ( (not br1 ) ? drang1  : 0 ) +
              ( (not br2 ) ? drang2  : 0 ) +
              ( (not br3 ) ? drang3  : 0 ) +
              ( (not br4 ) ? drang4  : 0 ) +
              ( (not br5 ) ? drang5  : 0 ) +
              ( (not br6 ) ? drang6  : 0 ) +
              ( (not br7 ) ? drang7  : 0 ) +
              ( (not br8 ) ? drang8  : 0 ) +
              ( (not br9 ) ? drang9  : 0 ) +
              ( (not br10) ? drang10 : 0 ) +
              ( (not br11) ? drang11 : 0 ) +
              ( (not br12) ? drang12 : 0 ) +
              ( (not br13) ? drang13 : 0 ) +
              ( (not br14) ? drang14 : 0 ) +
              ( (not br15) ? drang15 : 0 ) +
              ( (not br16) ? drang16 : 0 ) +
              ( (not br17) ? drang17 : 0 ) +
              ( (not br18) ? drang18 : 0 ) +
              ( (not br19) ? drang19 : 0 ) +
              ( (not br20) ? drang20 : 0 ) +
              ( (not br21) ? drang21 : 0 ) +
              ( (not br22) ? drang22 : 0 ) +
              ( (not br23) ? drang23 : 0 ) +
              ( (not br24) ? drang24 : 0 ) +
              ( (not br25) ? drang25 : 0 ) +
              ( (not br26) ? drang26 : 0 ) +
              ( (not br27) ? drang27 : 0 )

// Compute totalDiff and totalDrang (example shown earlier)
// … [your accumulation code for totalDiff and totalDrang]

// Ensure ratio is computed:
ratio = totalDrang != 0 ? totalDiff / totalDrang : na
newRatio = totalDrangAll != 0 ? totalAbsDiff / totalDrangAll : na

// Create table
var table signalTable = table.new(position.top_right, columns=10, rows=16, bgcolor=color.rgb(204, 230, 255), border_width=2, frame_color=color.black, frame_width=2) //color.rgb(204, 230, 255)

table.cell(signalTable, 0, 0, "S", text_color=color.new(#000000, 0), bgcolor=color.blue)
table.cell(signalTable, 1, 0, "A", text_color=color.new(#000000, 0), bgcolor=color.blue)
table.cell(signalTable, 2, 0, "M", text_color=color.new(#000000, 0), bgcolor=color.blue)
table.cell(signalTable, 3, 0, "D", text_color=color.new(#000000, 0), bgcolor=color.blue)
table.cell(signalTable, 4, 0, "Z", text_color=color.new(#000000, 0), bgcolor=color.blue)
table.cell(signalTable, 5, 0, "S", text_color=color.new(#000000, 0), bgcolor=color.blue)
table.cell(signalTable, 6, 0, "A", text_color=color.new(#000000, 0), bgcolor=color.blue)
table.cell(signalTable, 7, 0, "M", text_color=color.new(#000000, 0), bgcolor=color.blue)
table.cell(signalTable, 8, 0, "D", text_color=color.new(#000000, 0), bgcolor=color.blue)
table.cell(signalTable, 9, 0, "Z", text_color=color.new(#000000, 0), bgcolor=color.blue)

table.cell(signalTable, 9, 0, "Z", text_color=color.new(#000000, 0), bgcolor=color.blue)
table.cell(signalTable, 9, 0, "Z", text_color=color.new(#000000, 0), bgcolor=color.blue)
table.cell(signalTable, 9, 0, "Z", text_color=color.new(#000000, 0), bgcolor=color.blue)

// Populate table header
if barstate.islast
    // Clear the table to remove old values
    table.clear(signalTable, 3, 1, 3, 14)
    table.clear(signalTable, 4, 1, 4, 14)
    table.clear(signalTable, 8, 1, 8, 14)
    table.clear(signalTable, 9, 1, 9, 14)
    table.cell(signalTable, 0, 15, str.tostring(currentHour, "#") + ":" + str.tostring(currentMinute, "#"), text_color = #000000)
    // Populate table for each symbol
    // Symbol0 (row 1)
    table.cell(signalTable, 0, 1, str.substring(symbol0, 3, str.length(symbol0)), text_color=br0 ? #000000 : color.new(color.white, 55), bgcolor=zone0 == 0 ? color.maroon : zone0 == 1 ? color.red : zone0 == 5 ? color.blue : zone0 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 1, 1, zone0 == 0 ? "⮟" : zone0 == 1 ? "◆" : zone0 == 2 ? "■" : zone0 == 4 ? "■" : zone0 == 5 ? "◆" : zone0 == 6 ? "⮝" : str.tostring(zone0), text_color = zone0 == 0 ? color.maroon : zone0 == 1 ? color.red : zone0 == 2 ? color.purple : zone0 == 4 ? color.green : zone0 == 5 ? color.blue : zone0 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 2, 1, str.tostring(mid0, "#.#####") + " (" + str.tostring(drang0/2, "#.#") + ")",  text_color=br0 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 3, 1, str.tostring(diff0, "#.#"),  text_color=br0 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 4, 1, "●",  text_color=deezcrap0 == 6 ? color.green : deezcrap0 == 5 ? #0000ff : deezcrap0 == 4 ? color.rgb(48, 162, 255) : deezcrap0 == 2 ? color.rgb(255, 112, 112) : deezcrap0 == 1 ? #ff0000 : deezcrap0 == 0 ? color.orange : color.gray, bgcolor=tryk0 == 2 ? color.new(color.blue, 85) : tryk0 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol1 (row 2)
    table.cell(signalTable, 0, 2, str.substring(symbol1, 3, str.length(symbol1)), text_color=br1 ? #000000 : color.new(color.white, 55), bgcolor=zone1 == 0 ? color.maroon : zone1 == 1 ? color.red : zone1 == 5 ? color.blue : zone1 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 1, 2, zone1 == 0 ? "⮟" : zone1 == 1 ? "◆" : zone1 == 2 ? "■" : zone1 == 4 ? "■" : zone1 == 5 ? "◆" : zone1 == 6 ? "⮝" : str.tostring(zone1), text_color = zone1 == 0 ? color.maroon : zone1 == 1 ? color.red : zone1 == 2 ? color.purple : zone1 == 4 ? color.green : zone1 == 5 ? color.blue : zone1 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 2, 2, str.tostring(mid1, "#.#####") + " (" + str.tostring(drang1/2, "#.#") + ")",  text_color=br1 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 3, 2, str.tostring(diff1, "#.#"),  text_color=br1 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 4, 2, "●",  text_color=deezcrap1 == 6 ? color.green : deezcrap1 == 5 ? #0000ff : deezcrap1 == 4 ? color.rgb(48, 162, 255) : deezcrap1 == 2 ? color.rgb(255, 112, 112) : deezcrap1 == 1 ? #ff0000 : deezcrap1 == 0 ? color.orange : color.gray, bgcolor=tryk1 == 2 ? color.new(color.blue, 85) : tryk1 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol2 (row 3)
    table.cell(signalTable, 0, 3, str.substring(symbol2, 3, str.length(symbol2)), text_color=br2 ? #000000 : color.new(color.white, 55), bgcolor=zone2 == 0 ? color.maroon : zone2 == 1 ? color.red : zone2 == 5 ? color.blue : zone2 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 1, 3, zone2 == 0 ? "⮟" : zone2 == 1 ? "◆" : zone2 == 2 ? "■" : zone2 == 4 ? "■" : zone2 == 5 ? "◆" : zone2 == 6 ? "⮝" : str.tostring(zone2), text_color = zone2 == 0 ? color.maroon : zone2 == 1 ? color.red : zone2 == 2 ? color.purple : zone2 == 4 ? color.green : zone2 == 5 ? color.blue : zone2 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 2, 3, str.tostring(mid2, "#.#####") + " (" + str.tostring(drang2/2, "#.#") + ")",  text_color=br2 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 3, 3, str.tostring(diff2, "#.#"),  text_color=br2 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 4, 3, "●",  text_color=deezcrap2 == 6 ? color.green : deezcrap2 == 5 ? #0000ff : deezcrap2 == 4 ? color.rgb(48, 162, 255) : deezcrap2 == 2 ? color.rgb(255, 112, 112) : deezcrap2 == 1 ? #ff0000 : deezcrap2 == 0 ? color.orange : color.gray, bgcolor=tryk2 == 2 ? color.new(color.blue, 85) : tryk2 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol3 (row 4)
    table.cell(signalTable, 0, 4, str.substring(symbol3, 3, str.length(symbol3)), text_color=br3 ? #000000 : color.new(color.white, 55), bgcolor=zone3 == 0 ? color.maroon : zone3 == 1 ? color.red : zone3 == 5 ? color.blue : zone3 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 1, 4, zone3 == 0 ? "⮟" : zone3 == 1 ? "◆" : zone3 == 2 ? "■" : zone3 == 4 ? "■" : zone3 == 5 ? "◆" : zone3 == 6 ? "⮝" : str.tostring(zone3), text_color = zone3 == 0 ? color.maroon : zone3 == 1 ? color.red : zone3 == 2 ? color.purple : zone3 == 4 ? color.green : zone3 == 5 ? color.blue : zone3 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 2, 4, str.tostring(mid3, "#.#####") + " (" + str.tostring(drang3/2, "#.#") + ")",  text_color=br3 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 3, 4, str.tostring(diff3, "#.#"),  text_color=br3 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 4, 4, "●",  text_color=deezcrap3 == 6 ? color.green : deezcrap3 == 5 ? #0000ff : deezcrap3 == 4 ? color.rgb(48, 162, 255) : deezcrap3 == 2 ? color.rgb(255, 112, 112) : deezcrap3 == 1 ? #ff0000 : deezcrap3 == 0 ? color.orange : color.gray, bgcolor=tryk3 == 2 ? color.new(color.blue, 85) : tryk3 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol4 (row 5)
    table.cell(signalTable, 0, 5, str.substring(symbol4, 3, str.length(symbol4)), text_color=br4 ? #000000 : color.new(color.white, 55), bgcolor=zone4 == 0 ? color.maroon : zone4 == 1 ? color.red : zone4 == 5 ? color.blue : zone4 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 1, 5, zone4 == 0 ? "⮟" : zone4 == 1 ? "◆" : zone4 == 2 ? "■" : zone4 == 4 ? "■" : zone4 == 5 ? "◆" : zone4 == 6 ? "⮝" : str.tostring(zone4), text_color = zone4 == 0 ? color.maroon : zone4 == 1 ? color.red : zone4 == 2 ? color.purple : zone4 == 4 ? color.green : zone4 == 5 ? color.blue : zone4 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 2, 5, str.tostring(mid4, "#.#####") + " (" + str.tostring(drang4/2, "#.#") + ")",  text_color=br4 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 3, 5, str.tostring(diff4, "#.#"),  text_color=br4 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 4, 5, "●",  text_color=deezcrap4 == 6 ? color.green : deezcrap4 == 5 ? #0000ff : deezcrap4 == 4 ? color.rgb(48, 162, 255) : deezcrap4 == 2 ? color.rgb(255, 112, 112) : deezcrap4 == 1 ? #ff0000 : deezcrap4 == 0 ? color.orange : color.gray, bgcolor=tryk4 == 2 ? color.new(color.blue, 85) : tryk4 == 0 ? color.new(color.red, 85) : color.gray)
    
    // Symbol5 (row 6)
    table.cell(signalTable, 0, 6, str.substring(symbol5, 3, str.length(symbol5)), text_color=br5 ? #000000 : color.new(color.white, 55), bgcolor=zone5 == 0 ? color.maroon : zone5 == 1 ? color.red : zone5 == 5 ? color.blue : zone5 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 1, 6, zone5 == 0 ? "⮟" : zone5 == 1 ? "◆" : zone5 == 2 ? "■" : zone5 == 4 ? "■" : zone5 == 5 ? "◆" : zone5 == 6 ? "⮝" : str.tostring(zone5), text_color = zone5 == 0 ? color.maroon : zone5 == 1 ? color.red : zone5 == 2 ? color.purple : zone5 == 4 ? color.green : zone5 == 5 ? color.blue : zone5 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 2, 6, str.tostring(mid5, "#.#####") + " (" + str.tostring(drang5/2, "#.#") + ")",  text_color=br5 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 3, 6, str.tostring(diff5, "#.#"),  text_color=br5 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 4, 6, "●",  text_color=deezcrap5 == 6 ? color.green : deezcrap5 == 5 ? #0000ff : deezcrap5 == 4 ? color.rgb(48, 162, 255) : deezcrap5 == 2 ? color.rgb(255, 112, 112) : deezcrap5 == 1 ? #ff0000 : deezcrap5 == 0 ? color.orange : color.gray, bgcolor=tryk5 == 2 ? color.new(color.blue, 85) : tryk5 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol6 (row 7)
    table.cell(signalTable, 0, 7, str.substring(symbol6, 3, str.length(symbol6)), text_color=br6 ? #000000 : color.new(color.white, 55), bgcolor=zone6 == 0 ? color.maroon : zone6 == 1 ? color.red : zone6 == 5 ? color.blue : zone6 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 1, 7, zone6 == 0 ? "⮟" : zone6 == 1 ? "◆" : zone6 == 2 ? "■" : zone6 == 4 ? "■" : zone6 == 5 ? "◆" : zone6 == 6 ? "⮝" : str.tostring(zone6), text_color = zone6 == 0 ? color.maroon : zone6 == 1 ? color.red : zone6 == 2 ? color.purple : zone6 == 4 ? color.green : zone6 == 5 ? color.blue : zone6 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 2, 7, str.tostring(mid6, "#.###") + " (" + str.tostring(drang6/2, "#.#") + ")",  text_color=br6 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 3, 7, str.tostring(diff6, "#.#"),  text_color=br6 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 4, 7, "●",  text_color=deezcrap6 == 6 ? color.green : deezcrap6 == 5 ? #0000ff : deezcrap6 == 4 ? color.rgb(48, 162, 255) : deezcrap6 == 2 ? color.rgb(255, 112, 112) : deezcrap6 == 1 ? #ff0000 : deezcrap6 == 0 ? color.orange : color.gray, bgcolor=tryk6 == 2 ? color.new(color.blue, 85) : tryk6 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol7 (row 8)
    table.cell(signalTable, 0, 8, str.substring(symbol7, 3, str.length(symbol7)), text_color=br7 ? #000000 : color.new(color.white, 55), bgcolor=zone7 == 0 ? color.maroon : zone7 == 1 ? color.red : zone7 == 5 ? color.blue : zone7 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 1, 8, zone7 == 0 ? "⮟" : zone7 == 1 ? "◆" : zone7 == 2 ? "■" : zone7 == 4 ? "■" : zone7 == 5 ? "◆" : zone7 == 6 ? "⮝" : str.tostring(zone7), text_color = zone7 == 0 ? color.maroon : zone7 == 1 ? color.red : zone7 == 2 ? color.purple : zone7 == 4 ? color.green : zone7 == 5 ? color.blue : zone7 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 2, 8, str.tostring(mid7, "#.#####") + " (" + str.tostring(drang7/2, "#.#") + ")",  text_color=br7 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 3, 8, str.tostring(diff7, "#.#"),  text_color=br7 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 4, 8, "●",  text_color=deezcrap7 == 6 ? color.green : deezcrap7 == 5 ? #0000ff : deezcrap7 == 4 ? color.rgb(48, 162, 255) : deezcrap7 == 2 ? color.rgb(255, 112, 112) : deezcrap7 == 1 ? #ff0000 : deezcrap7 == 0 ? color.orange : color.gray, bgcolor=tryk7 == 2 ? color.new(color.blue, 85) : tryk7 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol8 (row 9)
    table.cell(signalTable, 0, 9, str.substring(symbol8, 3, str.length(symbol8)), text_color=br8 ? #000000 : color.new(color.white, 55), bgcolor=zone8 == 0 ? color.maroon : zone8 == 1 ? color.red : zone8 == 5 ? color.blue : zone8 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 1, 9, zone8 == 0 ? "⮟" : zone8 == 1 ? "◆" : zone8 == 2 ? "■" : zone8 == 4 ? "■" : zone8 == 5 ? "◆" : zone8 == 6 ? "⮝" : str.tostring(zone8), text_color = zone8 == 0 ? color.maroon : zone8 == 1 ? color.red : zone8 == 2 ? color.purple : zone8 == 4 ? color.green : zone8 == 5 ? color.blue : zone8 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 2, 9, str.tostring(mid8, "#.#####") + " (" + str.tostring(drang8/2, "#.#") + ")",  text_color=br8 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 3, 9, str.tostring(diff8, "#.#"),  text_color=br8 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 4, 9, "●",  text_color=deezcrap8 == 6 ? color.green : deezcrap8 == 5 ? #0000ff : deezcrap8 == 4 ? color.rgb(48, 162, 255) : deezcrap8 == 2 ? color.rgb(255, 112, 112) : deezcrap8 == 1 ? #ff0000 : deezcrap8 == 0 ? color.orange : color.gray, bgcolor=tryk8 == 2 ? color.new(color.blue, 85) : tryk8 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol9 (row 10)
    table.cell(signalTable, 0, 10, str.substring(symbol9, 3, str.length(symbol9)), text_color=br9 ? #000000 : color.new(color.white, 55), bgcolor=zone9 == 0 ? color.maroon : zone9 == 1 ? color.red : zone9 == 5 ? color.blue : zone9 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 1, 10, zone9 == 0 ? "⮟" : zone9 == 1 ? "◆" : zone9 == 2 ? "■" : zone9 == 4 ? "■" : zone9 == 5 ? "◆" : zone9 == 6 ? "⮝" : str.tostring(zone9), text_color = zone9 == 0 ? color.maroon : zone9 == 1 ? color.red : zone9 == 2 ? color.purple : zone9 == 4 ? color.green : zone9 == 5 ? color.blue : zone9 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 2, 10, str.tostring(mid9, "#.#####") + " (" + str.tostring(drang9/2, "#.#") + ")",  text_color=br9 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 3, 10, str.tostring(diff9, "#.#"),  text_color=br9 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 4, 10, "●",  text_color=deezcrap9 == 6 ? color.green : deezcrap9 == 5 ? #0000ff : deezcrap9 == 4 ? color.rgb(48, 162, 255) : deezcrap9 == 2 ? color.rgb(255, 112, 112) : deezcrap9 == 1 ? #ff0000 : deezcrap9 == 0 ? color.orange : color.gray, bgcolor=tryk9 == 2 ? color.new(color.blue, 85) : tryk9 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol10 (row 11)
    table.cell(signalTable, 0, 11, str.substring(symbol10, 3, str.length(symbol10)), text_color=br10 ? #000000 : color.new(color.white, 55), bgcolor=zone10 == 0 ? color.maroon : zone10 == 1 ? color.red : zone10 == 5 ? color.blue : zone10 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 1, 11, zone10 == 0 ? "⮟" : zone10 == 1 ? "◆" : zone10 == 2 ? "■" : zone10 == 4 ? "■" : zone10 == 5 ? "◆" : zone10 == 6 ? "⮝" : str.tostring(zone10), text_color = zone10 == 0 ? color.maroon : zone10 == 1 ? color.red : zone10 == 2 ? color.purple : zone10 == 4 ? color.green : zone10 == 5 ? color.blue : zone10 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 2, 11, str.tostring(mid10, "#.#####") + " (" + str.tostring(drang10/2, "#.#") + ")",  text_color=br10 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 3, 11, str.tostring(diff10, "#.#"),  text_color=br10 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 4, 11, "●",  text_color=deezcrap10 == 6 ? color.green : deezcrap10 == 5 ? #0000ff : deezcrap10 == 4 ? color.rgb(48, 162, 255) : deezcrap10 == 2 ? color.rgb(255, 112, 112) : deezcrap10 == 1 ? #ff0000 : deezcrap10 == 0 ? color.orange : color.gray, bgcolor=tryk10 == 2 ? color.new(color.blue, 85) : tryk10 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol11 (row 12)
    table.cell(signalTable, 0, 12, str.substring(symbol11, 3, str.length(symbol11)), text_color=br11 ? #000000 : color.new(color.white, 55), bgcolor=zone11 == 0 ? color.maroon : zone11 == 1 ? color.red : zone11 == 5 ? color.blue : zone11 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 1, 12, zone11 == 0 ? "⮟" : zone11 == 1 ? "◆" : zone11 == 2 ? "■" : zone11 == 4 ? "■" : zone11 == 5 ? "◆" : zone11 == 6 ? "⮝" : str.tostring(zone11), text_color = zone11 == 0 ? color.maroon : zone11 == 1 ? color.red : zone11 == 2 ? color.purple : zone11 == 4 ? color.green : zone11 == 5 ? color.blue : zone11 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 2, 12, str.tostring(mid11, "#.#####") + " (" + str.tostring(drang11/2, "#.#") + ")",  text_color=br11 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 3, 12, str.tostring(diff11, "#.#"),  text_color=br11 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 4, 12, "●",  text_color=deezcrap11 == 6 ? color.green : deezcrap11 == 5 ? #0000ff : deezcrap11 == 4 ? color.rgb(48, 162, 255) : deezcrap11 == 2 ? color.rgb(255, 112, 112) : deezcrap11 == 1 ? #ff0000 : deezcrap11 == 0 ? color.orange : color.gray, bgcolor=tryk11 == 2 ? color.new(color.blue, 85) : tryk11 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol12 (row 13)
    table.cell(signalTable, 0, 13, str.substring(symbol12, 3, str.length(symbol12)), text_color=br12 ? #000000 : color.new(color.white, 55), bgcolor=zone12 == 0 ? color.maroon : zone12 == 1 ? color.red : zone12 == 5 ? color.blue : zone12 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 1, 13, zone12 == 0 ? "⮟" : zone12 == 1 ? "◆" : zone12 == 2 ? "■" : zone12 == 4 ? "■" : zone12 == 5 ? "◆" : zone12 == 6 ? "⮝" : str.tostring(zone12), text_color = zone12 == 0 ? color.maroon : zone12 == 1 ? color.red : zone12 == 2 ? color.purple : zone12 == 4 ? color.green : zone12 == 5 ? color.blue : zone12 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 2, 13, str.tostring(mid12, "#.###") + " (" + str.tostring(drang12/2, "#.#") + ")",  text_color=br12 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 3, 13, str.tostring(diff12, "#.#"),  text_color=br12 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 4, 13, "●",  text_color=deezcrap12 == 6 ? color.green : deezcrap12 == 5 ? #0000ff : deezcrap12 == 4 ? color.rgb(48, 162, 255) : deezcrap12 == 2 ? color.rgb(255, 112, 112) : deezcrap12 == 1 ? #ff0000 : deezcrap12 == 0 ? color.orange : color.gray, bgcolor=tryk12 == 2 ? color.new(color.blue, 85) : tryk12 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol13 (row 14)
    table.cell(signalTable, 0, 14, str.substring(symbol13, 3, str.length(symbol13)), text_color=br13 ? #000000 : color.new(color.white, 55), bgcolor=zone13 == 0 ? color.maroon : zone13 == 1 ? color.red : zone13 == 5 ? color.blue : zone13 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 1, 14, zone13 == 0 ? "⮟" : zone13 == 1 ? "◆" : zone13 == 2 ? "■" : zone13 == 4 ? "■" : zone13 == 5 ? "◆" : zone13 == 6 ? "⮝" : str.tostring(zone13), text_color = zone13 == 0 ? color.maroon : zone13 == 1 ? color.red : zone13 == 2 ? color.purple : zone13 == 4 ? color.green : zone13 == 5 ? color.blue : zone13 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 2, 14, str.tostring(mid13, "#.#####") + " (" + str.tostring(drang13/2, "#.#") + ")",  text_color=br13 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 3, 14, str.tostring(diff13, "#.#"),  text_color=br13 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 4, 14, "●",  text_color=deezcrap13 == 6 ? color.green : deezcrap13 == 5 ? #0000ff : deezcrap13 == 4 ? color.rgb(48, 162, 255) : deezcrap13 == 2 ? color.rgb(255, 112, 112) : deezcrap13 == 1 ? #ff0000 : deezcrap13 == 0 ? color.orange : color.gray, bgcolor=tryk13 == 2 ? color.new(color.blue, 85) : tryk13 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol14 (row 15)
    table.cell(signalTable, 5, 1, str.substring(symbol14, 3, str.length(symbol14)), text_color=br14 ? #000000 : color.new(color.white, 55), bgcolor=zone14 == 0 ? color.maroon : zone14 == 1 ? color.red : zone14 == 5 ? color.blue : zone14 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 6, 1, zone14 == 0 ? "⮟" : zone14 == 1 ? "◆" : zone14 == 2 ? "■" : zone14 == 4 ? "■" : zone14 == 5 ? "◆" : zone14 == 6 ? "⮝" : str.tostring(zone14), text_color = zone14 == 0 ? color.maroon : zone14 == 1 ? color.red : zone14 == 2 ? color.purple : zone14 == 4 ? color.green : zone14 == 5 ? color.blue : zone14 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 7, 1, str.tostring(mid14, "#.#####") + " (" + str.tostring(drang14/2, "#.#") + ")",  text_color=br14 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 8, 1, str.tostring(diff14, "#.#"),  text_color=br14 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 9, 1, "●",  text_color=deezcrap14 == 6 ? color.green : deezcrap14 == 5 ? #0000ff : deezcrap14 == 4 ? color.rgb(48, 162, 255) : deezcrap14 == 2 ? color.rgb(255, 112, 112) : deezcrap14 == 1 ? #ff0000 : deezcrap14 == 0 ? color.orange : color.gray, bgcolor=tryk14 == 2 ? color.new(color.blue, 85) : tryk14 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol15 (row 2)
    table.cell(signalTable, 5, 2, str.substring(symbol15, 3, str.length(symbol15)), text_color=br15 ? #000000 : color.new(color.white, 55), bgcolor=zone15 == 0 ? color.maroon : zone15 == 1 ? color.red : zone15 == 5 ? color.blue : zone15 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 6, 2, zone15 == 0 ? "⮟" : zone15 == 1 ? "◆" : zone15 == 2 ? "■" : zone15 == 4 ? "■" : zone15 == 5 ? "◆" : zone15 == 6 ? "⮝" : str.tostring(zone15), text_color = zone15 == 0 ? color.maroon : zone15 == 1 ? color.red : zone15 == 2 ? color.purple : zone15 == 4 ? color.green : zone15 == 5 ? color.blue : zone15 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 7, 2, str.tostring(mid15, "#.#####") + " (" + str.tostring(drang15/2, "#.#") + ")",  text_color=br15 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 8, 2, str.tostring(diff15, "#.#"),  text_color=br15 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 9, 2, "●",  text_color=deezcrap15 == 6 ? color.green : deezcrap15 == 5 ? #0000ff : deezcrap15 == 4 ? color.rgb(48, 162, 255) : deezcrap15 == 2 ? color.rgb(255, 112, 112) : deezcrap15 == 1 ? #ff0000 : deezcrap15 == 0 ? color.orange : color.gray, bgcolor=tryk15 == 2 ? color.new(color.blue, 85) : tryk15 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol16 (row 3)
    table.cell(signalTable, 5, 3, str.substring(symbol16, 3, str.length(symbol16)), text_color=br16 ? #000000 : color.new(color.white, 55), bgcolor=zone16 == 0 ? color.maroon : zone16 == 1 ? color.red : zone16 == 5 ? color.blue : zone16 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 6, 3, zone16 == 0 ? "⮟" : zone16 == 1 ? "◆" : zone16 == 2 ? "■" : zone16 == 4 ? "■" : zone16 == 5 ? "◆" : zone16 == 6 ? "⮝" : str.tostring(zone16), text_color = zone16 == 0 ? color.maroon : zone16 == 1 ? color.red : zone16 == 2 ? color.purple : zone16 == 4 ? color.green : zone16 == 5 ? color.blue : zone16 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 7, 3, str.tostring(mid16, "#.#####") + " (" + str.tostring(drang16/2, "#.#") + ")",  text_color=br16 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 8, 3, str.tostring(diff16, "#.#"),  text_color=br16 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 9, 3, "●",  text_color=deezcrap16 == 6 ? color.green : deezcrap16 == 5 ? #0000ff : deezcrap16 == 4 ? color.rgb(48, 162, 255) : deezcrap16 == 2 ? color.rgb(255, 112, 112) : deezcrap16 == 1 ? #ff0000 : deezcrap16 == 0 ? color.orange : color.gray, bgcolor=tryk16 == 2 ? color.new(color.blue, 85) : tryk16 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol17 (row 4)
    table.cell(signalTable, 5, 4, str.substring(symbol17, 3, str.length(symbol17)), text_color=br17 ? #000000 : color.new(color.white, 55), bgcolor=zone17 == 0 ? color.maroon : zone17 == 1 ? color.red : zone17 == 5 ? color.blue : zone17 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 6, 4, zone17 == 0 ? "⮟" : zone17 == 1 ? "◆" : zone17 == 2 ? "■" : zone17 == 4 ? "■" : zone17 == 5 ? "◆" : zone17 == 6 ? "⮝" : str.tostring(zone17), text_color = zone17 == 0 ? color.maroon : zone17 == 1 ? color.red : zone17 == 2 ? color.purple : zone17 == 4 ? color.green : zone17 == 5 ? color.blue : zone17 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 7, 4, str.tostring(mid17, "#.###") + " (" + str.tostring(drang17/2, "#.#") + ")",  text_color=br17 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 8, 4, str.tostring(diff17, "#.#"),  text_color=br17 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 9, 4, "●",  text_color=deezcrap17 == 6 ? color.green : deezcrap17 == 5 ? #0000ff : deezcrap17 == 4 ? color.rgb(48, 162, 255) : deezcrap17 == 2 ? color.rgb(255, 112, 112) : deezcrap17 == 1 ? #ff0000 : deezcrap17 == 0 ? color.orange : color.gray, bgcolor=tryk17 == 2 ? color.new(color.blue, 85) : tryk17 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol18 (row 5)
    table.cell(signalTable, 5, 5, str.substring(symbol18, 3, str.length(symbol18)), text_color=br18 ? #000000 : color.new(color.white, 55), bgcolor=zone18 == 0 ? color.maroon : zone18 == 1 ? color.red : zone18 == 5 ? color.blue : zone18 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 6, 5, zone18 == 0 ? "⮟" : zone18 == 1 ? "◆" : zone18 == 2 ? "■" : zone18 == 4 ? "■" : zone18 == 5 ? "◆" : zone18 == 6 ? "⮝" : str.tostring(zone18), text_color = zone18 == 0 ? color.maroon : zone18 == 1 ? color.red : zone18 == 2 ? color.purple : zone18 == 4 ? color.green : zone18 == 5 ? color.blue : zone18 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 7, 5, str.tostring(mid18, "#.#####") + " (" + str.tostring(drang18/2, "#.#") + ")",  text_color=br18 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 8, 5, str.tostring(diff18, "#.#"),  text_color=br18 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 9, 5, "●",  text_color=deezcrap18 == 6 ? color.green : deezcrap18 == 5 ? #0000ff : deezcrap18 == 4 ? color.rgb(48, 162, 255) : deezcrap18 == 2 ? color.rgb(255, 112, 112) : deezcrap18 == 1 ? #ff0000 : deezcrap18 == 0 ? color.orange : color.gray, bgcolor=tryk18 == 2 ? color.new(color.blue, 85) : tryk18 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol19 (row 6)
    table.cell(signalTable, 5, 6, str.substring(symbol19, 3, str.length(symbol19)), text_color=br19 ? #000000 : color.new(color.white, 55), bgcolor=zone19 == 0 ? color.maroon : zone19 == 1 ? color.red : zone19 == 5 ? color.blue : zone19 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 6, 6, zone19 == 0 ? "⮟" : zone19 == 1 ? "◆" : zone19 == 2 ? "■" : zone19 == 4 ? "■" : zone19 == 5 ? "◆" : zone19 == 6 ? "⮝" : str.tostring(zone19), text_color = zone19 == 0 ? color.maroon : zone19 == 1 ? color.red : zone19 == 2 ? color.purple : zone19 == 4 ? color.green : zone19 == 5 ? color.blue : zone19 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 7, 6, str.tostring(mid19, "#.#####") + " (" + str.tostring(drang19/2, "#.#") + ")",  text_color=br19 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 8, 6, str.tostring(diff19, "#.#"),  text_color=br19 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 9, 6, "●",  text_color=deezcrap19 == 6 ? color.green : deezcrap19 == 5 ? #0000ff : deezcrap19 == 4 ? color.rgb(48, 162, 255) : deezcrap19 == 2 ? color.rgb(255, 112, 112) : deezcrap19 == 1 ? #ff0000 : deezcrap19 == 0 ? color.orange : color.gray, bgcolor=tryk19 == 2 ? color.new(color.blue, 85) : tryk19 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol20 (row 7)
    table.cell(signalTable, 5, 7, str.substring(symbol20, 3, str.length(symbol20)), text_color=br20 ? #000000 : color.new(color.white, 55), bgcolor=zone20 == 0 ? color.maroon : zone20 == 1 ? color.red : zone20 == 5 ? color.blue : zone20 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 6, 7, zone20 == 0 ? "⮟" : zone20 == 1 ? "◆" : zone20 == 2 ? "■" : zone20 == 4 ? "■" : zone20 == 5 ? "◆" : zone20 == 6 ? "⮝" : str.tostring(zone20), text_color = zone20 == 0 ? color.maroon : zone20 == 1 ? color.red : zone20 == 2 ? color.purple : zone20 == 4 ? color.green : zone20 == 5 ? color.blue : zone20 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 7, 7, str.tostring(mid20, "#.#####") + " (" + str.tostring(drang20/2, "#.#") + ")",  text_color=br20 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 8, 7, str.tostring(diff20, "#.#"),  text_color=br20 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 9, 7, "●",  text_color=deezcrap20 == 6 ? color.green : deezcrap20 == 5 ? #0000ff : deezcrap20 == 4 ? color.rgb(48, 162, 255) : deezcrap20 == 2 ? color.rgb(255, 112, 112) : deezcrap20 == 1 ? #ff0000 : deezcrap20 == 0 ? color.orange : color.gray, bgcolor=tryk20 == 2 ? color.new(color.blue, 85) : tryk20 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol21 (row 8)
    table.cell(signalTable, 5, 8, str.substring(symbol21, 3, str.length(symbol21)), text_color=br21 ? #000000 : color.new(color.white, 55), bgcolor=zone21 == 0 ? color.maroon : zone21 == 1 ? color.red : zone21 == 5 ? color.blue : zone21 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 6, 8, zone21 == 0 ? "⮟" : zone21 == 1 ? "◆" : zone21 == 2 ? "■" : zone21 == 4 ? "■" : zone21 == 5 ? "◆" : zone21 == 6 ? "⮝" : str.tostring(zone21), text_color = zone21 == 0 ? color.maroon : zone21 == 1 ? color.red : zone21 == 2 ? color.purple : zone21 == 4 ? color.green : zone21 == 5 ? color.blue : zone21 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 7, 8, str.tostring(mid21, "#.###") + " (" + str.tostring(drang21/2, "#.#") + ")",  text_color=br21 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 8, 8, str.tostring(diff21, "#.#"),  text_color=br21 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 9, 8, "●",  text_color=deezcrap21 == 6 ? color.green : deezcrap21 == 5 ? #0000ff : deezcrap21 == 4 ? color.rgb(48, 162, 255) : deezcrap21 == 2 ? color.rgb(255, 112, 112) : deezcrap21 == 1 ? #ff0000 : deezcrap21 == 0 ? color.orange : color.gray, bgcolor=tryk21 == 2 ? color.new(color.blue, 85) : tryk21 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol22 (row 9)
    table.cell(signalTable, 5, 9, str.substring(symbol22, 3, str.length(symbol22)), text_color=br22 ? #000000 : color.new(color.white, 55), bgcolor=zone22 == 0 ? color.maroon : zone22 == 1 ? color.red : zone22 == 5 ? color.blue : zone22 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 6, 9, zone22 == 0 ? "⮟" : zone22 == 1 ? "◆" : zone22 == 2 ? "■" : zone22 == 4 ? "■" : zone22 == 5 ? "◆" : zone22 == 6 ? "⮝" : str.tostring(zone22), text_color = zone22 == 0 ? color.maroon : zone22 == 1 ? color.red : zone22 == 2 ? color.purple : zone22 == 4 ? color.green : zone22 == 5 ? color.blue : zone22 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 7, 9, str.tostring(mid22, "#.#####") + " (" + str.tostring(drang22/2, "#.#") + ")",  text_color=br22 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 8, 9, str.tostring(diff22, "#.#"),  text_color=br22 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 9, 9, "●",  text_color=deezcrap22 == 6 ? color.green : deezcrap22 == 5 ? #0000ff : deezcrap22 == 4 ? color.rgb(48, 162, 255) : deezcrap22 == 2 ? color.rgb(255, 112, 112) : deezcrap22 == 1 ? #ff0000 : deezcrap22 == 0 ? color.orange : color.gray, bgcolor=tryk22 == 2 ? color.new(color.blue, 85) : tryk22 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol23 (row 10)
    table.cell(signalTable, 5, 10, str.substring(symbol23, 3, str.length(symbol23)), text_color=br23 ? #000000 : color.new(color.white, 55), bgcolor=zone23 == 0 ? color.maroon : zone23 == 1 ? color.red : zone23 == 5 ? color.blue : zone23 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 6, 10, zone23 == 0 ? "⮟" : zone23 == 1 ? "◆" : zone23 == 2 ? "■" : zone23 == 4 ? "■" : zone23 == 5 ? "◆" : zone23 == 6 ? "⮝" : str.tostring(zone23), text_color = zone23 == 0 ? color.maroon : zone23 == 1 ? color.red : zone23 == 2 ? color.purple : zone23 == 4 ? color.green : zone23 == 5 ? color.blue : zone23 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 7, 10, str.tostring(mid23, "#.#####") + " (" + str.tostring(drang23/2, "#.#") + ")",  text_color=br23 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 8, 10, str.tostring(diff23, "#.#"),  text_color=br23 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 9, 10, "●",  text_color=deezcrap23 == 6 ? color.green : deezcrap23 == 5 ? #0000ff : deezcrap23 == 4 ? color.rgb(48, 162, 255) : deezcrap23 == 2 ? color.rgb(255, 112, 112) : deezcrap23 == 1 ? #ff0000 : deezcrap23 == 0 ? color.orange : color.gray, bgcolor=tryk23 == 2 ? color.new(color.blue, 85) : tryk23 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol24 (row 11)
    table.cell(signalTable, 5, 11, str.substring(symbol24, 3, str.length(symbol24)), text_color=br24 ? #000000 : color.new(color.white, 55), bgcolor=zone24 == 0 ? color.maroon : zone24 == 1 ? color.red : zone24 == 5 ? color.blue : zone24 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 6, 11, zone24 == 0 ? "⮟" : zone24 == 1 ? "◆" : zone24 == 2 ? "■" : zone24 == 4 ? "■" : zone24 == 5 ? "◆" : zone24 == 6 ? "⮝" : str.tostring(zone24), text_color = zone24 == 0 ? color.maroon : zone24 == 1 ? color.red : zone24 == 2 ? color.purple : zone24 == 4 ? color.green : zone24 == 5 ? color.blue : zone24 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 7, 11, str.tostring(mid24, "#.###") + " (" + str.tostring(drang24/2, "#.#") + ")",  text_color=br24 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 8, 11, str.tostring(diff24, "#.#"),  text_color=br24 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 9, 11, "●",  text_color=deezcrap24 == 6 ? color.green : deezcrap24 == 5 ? #0000ff : deezcrap24 == 4 ? color.rgb(48, 162, 255) : deezcrap24 == 2 ? color.rgb(255, 112, 112) : deezcrap24 == 1 ? #ff0000 : deezcrap24 == 0 ? color.orange : color.gray, bgcolor=tryk24 == 2 ? color.new(color.blue, 85) : tryk24 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol25 (row 12)
    table.cell(signalTable, 5, 12, str.substring(symbol25, 3, str.length(symbol25)), text_color=br25 ? #000000 : color.new(color.white, 55), bgcolor=zone25 == 0 ? color.maroon : zone25 == 1 ? color.red : zone25 == 5 ? color.blue : zone25 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 6, 12, zone25 == 0 ? "⮟" : zone25 == 1 ? "◆" : zone25 == 2 ? "■" : zone25 == 4 ? "■" : zone25 == 5 ? "◆" : zone25 == 6 ? "⮝" : str.tostring(zone25), text_color = zone25 == 0 ? color.maroon : zone25 == 1 ? color.red : zone25 == 2 ? color.purple : zone25 == 4 ? color.green : zone25 == 5 ? color.blue : zone25 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 7, 12, str.tostring(mid25, "#.#####") + " (" + str.tostring(drang25/2, "#.#") + ")",  text_color=br25 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 8, 12, str.tostring(diff25, "#.#"),  text_color=br25 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 9, 12, "●",  text_color=deezcrap25 == 6 ? color.green : deezcrap25 == 5 ? #0000ff : deezcrap25 == 4 ? color.rgb(48, 162, 255) : deezcrap25 == 2 ? color.rgb(255, 112, 112) : deezcrap25 == 1 ? #ff0000 : deezcrap25 == 0 ? color.orange : color.gray, bgcolor=tryk25 == 2 ? color.new(color.blue, 85) : tryk25 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol26 (row 13)
    table.cell(signalTable, 5, 13, str.substring(symbol26, 3, str.length(symbol26)), text_color=br26 ? #000000 : color.new(color.white, 55), bgcolor=zone26 == 0 ? color.maroon : zone26 == 1 ? color.red : zone26 == 5 ? color.blue : zone26 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 6, 13, zone26 == 0 ? "⮟" : zone26 == 1 ? "◆" : zone26 == 2 ? "■" : zone26 == 4 ? "■" : zone26 == 5 ? "◆" : zone26 == 6 ? "⮝" : str.tostring(zone26), text_color = zone26 == 0 ? color.maroon : zone26 == 1 ? color.red : zone26 == 2 ? color.purple : zone26 == 4 ? color.green : zone26 == 5 ? color.blue : zone26 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 7, 13, str.tostring(mid26, "#.###") + " (" + str.tostring(drang26/2, "#.#") + ")",  text_color=br26 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 8, 13, str.tostring(diff26, "#.#"),  text_color=br26 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 9, 13, "●",  text_color=deezcrap26 == 6 ? color.green : deezcrap26 == 5 ? #0000ff : deezcrap26 == 4 ? color.rgb(48, 162, 255) : deezcrap26 == 2 ? color.rgb(255, 112, 112) : deezcrap26 == 1 ? #ff0000 : deezcrap26 == 0 ? color.orange : color.gray, bgcolor=tryk26 == 2 ? color.new(color.blue, 85) : tryk26 == 0 ? color.new(color.red, 85) : color.gray)

    // Symbol27 (row 14)
    table.cell(signalTable, 5, 14, str.substring(symbol27, 3, str.length(symbol27)), text_color=br27 ? #000000 : color.new(color.white, 55), bgcolor=zone27 == 0 ? color.maroon : zone27 == 1 ? color.red : zone27 == 5 ? color.blue : zone27 == 6 ? #0a0ab8 : color.gray)
    table.cell(signalTable, 6, 14, zone27 == 0 ? "⮟" : zone27 == 1 ? "◆" : zone27 == 2 ? "■" : zone27 == 4 ? "■" : zone27 == 5 ? "◆" : zone27 == 6 ? "⮝" : str.tostring(zone27), text_color = zone27 == 0 ? color.maroon : zone27 == 1 ? color.red : zone27 == 2 ? color.purple : zone27 == 4 ? color.green : zone27 == 5 ? color.blue : zone27 == 6 ? #0a0ab8 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 7, 14, str.tostring(mid27, "#.###") + " (" + str.tostring(drang27/2, "#.#") + ")",  text_color=br27 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 8, 14, str.tostring(diff27, "#.#"),  text_color=br27 ? #000000 : color.new(color.white, 55), bgcolor=color.gray)
    table.cell(signalTable, 9, 14, "●",  text_color=deezcrap27 == 6 ? color.green : deezcrap27 == 5 ? #0000ff : deezcrap27 == 4 ? color.rgb(48, 162, 255) : deezcrap27 == 2 ? color.rgb(255, 112, 112) : deezcrap27 == 1 ? #ff0000 : deezcrap27 == 0 ? color.orange : color.gray, bgcolor=tryk27 == 2 ? color.new(color.blue, 85) : tryk27 == 0 ? color.new(color.red, 85) : color.gray)

    //table.cell(signalTable, 0, 15, str.tostring(currentHour, "#"),  text_color=#000000, bgcolor=color.gray)


// Create a new table for the ratio (positioned at bottom right)
var table ratioTable = table.new(position.middle_right, 1, 14, bgcolor=color.new(color.rgb(204, 230, 255), 100), border_width=2, frame_color=color.new(color.black, 100), frame_width=2)



// On the last bar, populate the ratio table with the result.
if barstate.islast
    // Clear the table to remove old values
    table.clear(ratioTable, 0, 0, 0, 13)
    //table.clear(ratioTable, 1, 0, 1, 10)

    table.cell(ratioTable, 0, 0, "", text_color=color.new(color.black, 100), bgcolor=color.new(color.blue, 100))
    table.cell(ratioTable, 0, 1, "", text_color=color.new(color.black, 100), bgcolor=color.new(color.blue, 100))
    table.cell(ratioTable, 0, 2, "", text_color=color.new(color.black, 100), bgcolor=color.new(color.blue, 100))
    table.cell(ratioTable, 0, 3, "", text_color=color.new(color.black, 100), bgcolor=color.new(color.blue, 100))
    table.cell(ratioTable, 0, 4, "", text_color=color.new(color.black, 100), bgcolor=color.new(color.blue, 100))
    table.cell(ratioTable, 0, 5, "", text_color=color.new(color.black, 100), bgcolor=color.new(color.blue, 100))
    table.cell(ratioTable, 0, 6, "", text_color=color.new(color.black, 100), bgcolor=color.new(color.blue, 100))
    table.cell(ratioTable, 0, 7, "", text_color=color.new(color.black, 100), bgcolor=color.new(color.blue, 100))
    //table.cell(ratioTable, 0, 8, "", text_color=color.new(color.black, 100), bgcolor=color.new(color.blue, 100))
    table.cell(ratioTable, 0, 9, "ERatio = " + str.tostring(ratio, "#.###"), text_color=#000000, bgcolor=ratio >= 1 ? color.green : ratio < 1 ? color.red : color.blue)
    //table.cell(ratioTable, 0, 7, "Tdiff = " + str.tostring(totalDiff, "#.#"), text_color=#000000, bgcolor=color.blue)
    //table.cell(ratioTable, 0, 8, "Drang = " + str.tostring(totalDrang, "#.#"), text_color=#000000, bgcolor=color.blue)
    table.cell(ratioTable, 0, 12, "RRatio = " + str.tostring(newRatio, "#.###"), text_color=#000000, bgcolor=newRatio >= 0.5 ? color.green : newRatio < 0.5 ? color.red : color.blue)
    table.cell(ratioTable, 0, 13, "PRatio = " + str.tostring(ratio/newRatio, "#.###"), text_color=#000000, bgcolor=ratio >= newRatio ? color.yellow : ratio < newRatio ? color.red : color.blue)

var float eurb = 0.0
var float gbpb = 0.0
var float audb = 0.0
var float nzdb = 0.0
var float usdb = 0.0
var float cadb = 0.0
var float chfb = 0.0
var float jpyb = 0.0

eurb := pips0 + pips1 + pips2 + pips3 + pips4 + pips5 + pips6
gbpb := -pips1 + pips7 + pips8 + pips9 + pips10 + pips11 + pips12
audb := -pips2 - pips8 + pips13 + pips14 + pips15 + pips16 + pips17
nzdb := -pips3 - pips9 - pips14 + pips18 + pips19 + pips20 + pips21
usdb := -pips0 - pips7 - pips13 - pips18 + pips22 + pips23 + pips24
cadb := -pips4 - pips10 - pips15 - pips19 - pips22 + pips25 + pips26
chfb := -pips5 - pips11 - pips16 - pips20 - pips23 - pips25 + pips27
jpyb := -pips6 - pips12 - pips17 - pips21 - pips24 - pips26 - pips27

var float egan = 0.0
var float ucfj = 0.0

egan := ( (pips0 >= 0) ? pips0  : 0 ) +
              ( (pips1 >= 0) ? pips1  : 0 ) +
              ( (pips2 >= 0) ? pips2  : 0 ) +
              ( (pips3 >= 0) ? pips3  : 0 ) +
              ( (pips4 >= 0) ? pips4  : 0 ) +
              ( (pips5 >= 0) ? pips5  : 0 ) +
              ( (pips6 >= 0) ? pips6  : 0 ) +
              ( (pips7 >= 0) ? pips7  : 0 ) +
              ( (pips8 >= 0) ? pips8  : 0 ) +
              ( (pips9 >= 0) ? pips9  : 0 ) +
              ( (pips10>= 0) ? pips10 : 0 ) +
              ( (pips11>= 0) ? pips11 : 0 ) +
              ( (pips12>= 0) ? pips12 : 0 ) +
              ( (pips13>= 0) ? pips13 : 0 ) +
              ( (pips14>= 0) ? pips14 : 0 ) +
              ( (pips15>= 0) ? pips15 : 0 ) +
              ( (pips16>= 0) ? pips16 : 0 ) +
              ( (pips17>= 0) ? pips17 : 0 ) +
              ( (pips18>= 0) ? pips18 : 0 ) +
              ( (pips19>= 0) ? pips19 : 0 ) +
              ( (pips20>= 0) ? pips20 : 0 ) +
              ( (pips21>= 0) ? pips21 : 0 ) +
              ( (pips22>= 0) ? pips22 : 0 ) +
              ( (pips23>= 0) ? pips23 : 0 ) +
              ( (pips24>= 0) ? pips24 : 0 ) +
              ( (pips25>= 0) ? pips25 : 0 ) +
              ( (pips26>= 0) ? pips26 : 0 ) +
              ( (pips27>= 0) ? pips27 : 0 )

ucfj := ( (pips0 < 0) ? pips0  : 0 ) +
              ( (pips1 < 0) ? pips1  : 0 ) +
              ( (pips2 < 0) ? pips2  : 0 ) +
              ( (pips3 < 0) ? pips3  : 0 ) +
              ( (pips4 < 0) ? pips4  : 0 ) +
              ( (pips5 < 0) ? pips5  : 0 ) +
              ( (pips6 < 0) ? pips6  : 0 ) +
              ( (pips7 < 0) ? pips7  : 0 ) +
              ( (pips8 < 0) ? pips8  : 0 ) +
              ( (pips9 < 0) ? pips9  : 0 ) +
              ( (pips10< 0) ? pips10 : 0 ) +
              ( (pips11< 0) ? pips11 : 0 ) +
              ( (pips12< 0) ? pips12 : 0 ) +
              ( (pips13< 0) ? pips13 : 0 ) +
              ( (pips14< 0) ? pips14 : 0 ) +
              ( (pips15< 0) ? pips15 : 0 ) +
              ( (pips16< 0) ? pips16 : 0 ) +
              ( (pips17< 0) ? pips17 : 0 ) +
              ( (pips18< 0) ? pips18 : 0 ) +
              ( (pips19< 0) ? pips19 : 0 ) +
              ( (pips20< 0) ? pips20 : 0 ) +
              ( (pips21< 0) ? pips21 : 0 ) +
              ( (pips22< 0) ? pips22 : 0 ) +
              ( (pips23< 0) ? pips23 : 0 ) +
              ( (pips24< 0) ? pips24 : 0 ) +
              ( (pips25< 0) ? pips25 : 0 ) +
              ( (pips26< 0) ? pips26 : 0 ) +
              ( (pips27< 0) ? pips27 : 0 )

bsratio = (egan > math.abs(ucfj) and ucfj != 0) ? egan / math.abs(ucfj) : (math.abs(ucfj) > egan and egan != 0) ? ucfj / egan : na

var table basketTable = table.new(position.bottom_right, 8, 9, bgcolor=color.new(color.rgb(204, 230, 255), 100), border_width=2, frame_color=color.new(color.black, 100), frame_width=2)

table.cell(basketTable, 5, 4, "   ", text_color=color.new(#000000, 0), bgcolor=#000000)
table.cell(basketTable, 6, 4, "   ", text_color=color.new(#000000, 0), bgcolor=#000000)
table.cell(basketTable, 7, 4, "   ", text_color=color.new(#000000, 0), bgcolor=#000000)
table.cell(basketTable, 0, 0, "EUR", text_color=color.new(#000000, 0), bgcolor=color.blue)
table.cell(basketTable, 0, 1, "GBP", text_color=color.new(#000000, 0), bgcolor=color.red)
table.cell(basketTable, 0, 2, "AUD", text_color=color.new(#000000, 0), bgcolor=color.orange)
table.cell(basketTable, 0, 3, "NZD", text_color=color.new(#000000, 0), bgcolor=color.aqua)
table.cell(basketTable, 2, 0, "USD", text_color=color.new(#000000, 0), bgcolor=color.green)
table.cell(basketTable, 2, 1, "CAD", text_color=color.new(#000000, 0), bgcolor=color.purple)
table.cell(basketTable, 2, 2, "CHF", text_color=color.new(#000000, 0), bgcolor=color.gray)
table.cell(basketTable, 2, 3, "JPY", text_color=color.new(#000000, 0), bgcolor=color.yellow)
table.cell(basketTable, 0, 4, "BUY", text_color=color.new(#000000, 0), bgcolor=color.silver)
table.cell(basketTable, 2, 4, "SELL", text_color=color.new(#000000, 0), bgcolor=color.silver)
table.cell(basketTable, 0, 5, "N.EU", text_color=color.new(#000000, 0), bgcolor=color.maroon)
table.cell(basketTable, 0, 6, "OCEA", text_color=color.new(#000000, 0), bgcolor=color.yellow)
table.cell(basketTable, 0, 7, "ASIA", text_color=color.new(#000000, 0), bgcolor=color.white)
table.cell(basketTable, 0, 8, "EURO", text_color=color.new(#000000, 0), bgcolor=color.blue)
table.cell(basketTable, 2, 5, "N.AM", text_color=color.new(#000000, 0), bgcolor=color.purple)
table.cell(basketTable, 2, 6, "RISK", text_color=color.new(#000000, 0), bgcolor=color.red)
table.cell(basketTable, 2, 7, "FRIS", text_color=color.new(#000000, 0), bgcolor=color.red)
table.cell(basketTable, 2, 8, "COMS", text_color=color.new(#000000, 0), bgcolor=color.orange)

// On the last bar, populate the ratio table with the result.
if barstate.islast
    // Clear the table to remove old values
    table.clear(basketTable, 1, 0, 1, 8)
    table.clear(basketTable, 3, 0, 3, 8)
    table.clear(basketTable, 4, 4, 4, 4)

    table.cell(basketTable, 1, 0, str.tostring(eurb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.blue)
    table.cell(basketTable, 1, 1, str.tostring(gbpb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.red)
    table.cell(basketTable, 1, 2, str.tostring(audb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.orange)
    table.cell(basketTable, 1, 3, str.tostring(nzdb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.aqua)
    table.cell(basketTable, 3, 0, str.tostring(usdb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.green)
    table.cell(basketTable, 3, 1, str.tostring(cadb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.purple)
    table.cell(basketTable, 3, 2, str.tostring(chfb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.gray)
    table.cell(basketTable, 3, 3, str.tostring(jpyb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.yellow)

    table.cell(basketTable, 1, 5, str.tostring(eurb + gbpb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.maroon)
    table.cell(basketTable, 1, 6, str.tostring(audb + nzdb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.yellow)
    table.cell(basketTable, 1, 7, str.tostring(audb + nzdb + jpyb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.white)
    table.cell(basketTable, 1, 8, str.tostring(eurb + gbpb + chfb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.blue)
    table.cell(basketTable, 3, 5, str.tostring(usdb + cadb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.purple)
    table.cell(basketTable, 3, 6, str.tostring(chfb + jpyb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.red)
    table.cell(basketTable, 3, 7, str.tostring(chfb + jpyb + usdb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.red)
    table.cell(basketTable, 3, 8, str.tostring(audb + nzdb + cadb, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.orange)
    
    table.cell(basketTable, 1, 4, str.tostring(egan, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.silver)
    table.cell(basketTable, 3, 4, str.tostring(ucfj, "#.#"), text_color=color.new(#000000, 0), bgcolor=color.silver)
    table.cell(basketTable, 4, 4, str.tostring(bsratio, "#.##"), text_color=color.new(#000000, 0), bgcolor=bsratio >= 0 ? color.blue : color.red)