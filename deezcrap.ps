//@version=6
indicator("deezcrap", overlay = true)

// Input fields for customization
lookbackPeriod = input.int(120, title="Lookback Period for High/Low")
seriesIncrement = input.float(0.001, title="Series Increment/Decrement Value")

// Create custom series based on close price comparison
var float customSeries = na
//customSeries := na(customSeries[1]) ? 0 : customSeries[1] + (close > close[1] ? seriesIncrement : -seriesIncrement)

//flot = ta.sma(customSeries, 6)

// Calculate high and low over the lookback period
highLookback = ta.highest(high, lookbackPeriod)[6]
lowLookback = ta.lowest(low, lookbackPeriod)[6]

dist = highLookback - lowLookback
lowdist = lowLookback + 0.236 * dist
higdist = highLookback - 0.236 * dist
llow = lowLookback - 0.618 * dist
hhig = highLookback + 0.618 * dist
middist = lowLookback + ((highLookback - lowLookback) / 2)

drop = (open[1] < lowdist[1] or close[1] < lowdist[1]) and open < lowdist
rise = (open[1] > higdist[1] or close[1] > higdist[1]) and open > higdist
exceedu = (close[1] < highLookback[1] and close > highLookback) or (close[1] > highLookback[1] and close > highLookback)
exceedd = (close[1] > lowLookback[1] and close < lowLookback) or (close[1] < lowLookback[1] and close < lowLookback)
exceedu2 = close[1] > hhig[1]
exceedd2 = close[1] < llow[1]

//bgcolor(exceedu2 ? color.new(color.navy, 50) : exceedd2 ? color.new(color.maroon, 50) : exceedu ? color.new(color.green, 80) : exceedd ? color.new(color.orange, 80) : drop ? color.new(color.red, 80) : rise ? color.new(color.blue, 80) : na)

// Plot custom series as a line
p1 = plot(highLookback, title="Custom Series2", color=color.new(color.white, 0), linewidth=1)
p2 = plot(lowLookback, title="Custom Series2", color=color.new(color.white, 0), linewidth=1)
p3 = plot(lowdist, title="Custom Series2", color= color.new(color.yellow, 75), linewidth=1)
p4 = plot(higdist, title="Custom Series2", color= color.new(color.yellow, 75), linewidth=1)
//plot(llow, title="Custom Series2", color= color.gray, linewidth=1)
//plot(hhig, title="Custom Series2", color= color.gray, linewidth=1)
plot(middist, title="Custom Series2", color=color.purple, linewidth=2)

fill(p1, p4, color = color.new(color.gray, 70))
fill(p2, p3, color = color.new(color.gray, 70))