study("Decaying Rate of Change Non Linear Filter", overlay=true)
src = input(close, title="source")
roc_len = input(5, title="length for rate of change")
filt_len = input(30, title="filter length")
pow = input(1.00, title="Non linearity", minval=0.01, step=0.1)
perc_dec = input(0.10, title= "Coefficient percentage decay per period (as decimal)")

use_smooth = input(false, title="Smooth the filter?")
smooth_len = input(9, title= "Smoothing length")

f_ss(_src, _len) =>
    pi = 2 * asin(1)
    arg = sqrt(2) * pi / _len
    a1 = exp(-arg)
    b1 = 2 * a1 * cos(arg)
    c2 = b1
    c3 = -pow(a1, 2)
    c1 = 1 - c2 - c3
    ssf = 0.0
    ssf := c1_src+ c2 nz(ssf[1],nz(_src[1],_src)) + c3 * nz(ssf[2],nz(_src[2],nz(_src[1],_src)))
    ssf_out=bar_index>=3?ssf:na

f_filt(_src, _rlen, _len, _perc, _pow)=>
    _c1 = abs(_src-nz(_src[_rlen])) / nz(_src[_rlen])
    _c2 = pow(_c1,_pow)
    _num = 0.0
    _denom = 0.0
    for n = 0 to _len -1
        _c3 = _c2[n] * pow(1-_perc,n)
        _num := _num + nz(_src[n]) * _c3
        _denom := _denom + _c3
    _num / _denom

filt = f_filt(src,roc_len, filt_len, perc_dec, pow)

plot(use_smooth ? f_ss(filt,smooth_len) : filt, color=color.yellow, linewidth=2, title="FILTER")