//@version=6
indicator("SS Score & RSI Score Oscillator", shorttitle="SS", overlay=false)

// Calculate time 2 days ago
var int SECONDS_PER_DAY = 86400000
var int twoDaysAgo = time - (1 * SECONDS_PER_DAY)

// --- Input Parameters ---
smoothness  = input.float(0.33, "ATR Smoothness Factor", step=0.01)
atrLength   = input.int(21, "ATR Length", minval=1)
rsiLength   = input.int(14, "RSI Length", minval=1)
lookbackPeriod = input.int(120, "DC Lookback Period", minval=1)
// RSI thresholds (from Python logic)
rsiHighThresh    = input.float(70.0, "RSI High Threshold")
rsiMidHighThresh = input.float(55.9, "RSI Upper-Mid Threshold")
rsiMidLowThresh  = input.float(44.1, "RSI Lower-Mid Threshold")
rsiLowThresh     = input.float(30.0, "RSI Low Threshold")

//
normLookback = input.int(100, "Normalization Lookback Bars", minval=10)

// ===== Determine number of M5 bars per current bar =====
// For intraday charts, use: current timeframe (in minutes) / 5; otherwise, assume 288 (approx. one day of M5 bars).
int m5Bars = timeframe.isintraday ? int(timeframe.multiplier / 5) : 288

// ===== Calculate M5 low/high indexes =====
//var int m5_lowIdx = 0
//var int m5_highIdx = 0

var string firstMove = na

if bar_index > 0
    lowArray = request.security_lower_tf(syminfo.tickerid, "5", low)
    highArray = request.security_lower_tf(syminfo.tickerid, "5", high)

    // Find which came first - high or low
    
    if array.size(highArray) > 0 and array.size(lowArray) > 0  // Removed barstate conditions
        firstMove := na
        float highIndex = na
        float lowIndex = na
        float h1_high = high
        float h1_low = low
        
        // Loop through M5 bars to find first occurrence of H1 high/low
        for i = 0 to array.size(highArray) - 1
            if math.abs(array.get(highArray, i) - h1_high) < 0.0001 and na(highIndex)
                highIndex := i
            if math.abs(array.get(lowArray, i) - h1_low) < 0.0001 and na(lowIndex)
                lowIndex := i
        
        // Compare which index came first
        if not na(highIndex) and not na(lowIndex)
            firstMove := highIndex < lowIndex ? "H->L" : "L->H"

// ===== Determine Order =====
// If the lowest low occurred further in the past (i.e. index greater) than the highest high,
// then low came first: assign +1; if highest came first, assign -1; else 0. **INVERTED
//float order_val = m5_lowIdx > m5_highIdx ? 1 : (m5_highIdx > m5_lowIdx ? -1 : 0)

// Convert order_val into a descriptive string
// "L->H" for upward swing, "H->L" for downward swing, otherwise na.
//string currentOrder = na
//if order_val == 1
//    currentOrder := "L->H"
//else if order_val == -1
//    currentOrder := "H->L"
//else
//    currentOrder := "NA"

var float movementScore = 0.0
var int delta1 = 0
string prevOrder = firstMove[1]

if bar_index > 0
    if na(prevOrder) or na(firstMove)
        delta1 := 0
    else if prevOrder == "L->H" and firstMove == "L->H"
        delta1 := 2
    else if prevOrder == "H->L" and firstMove == "L->H"
        delta1 := 1
    else if prevOrder == "L->H" and firstMove == "H->L"
        delta1 := -1
    else if prevOrder == "H->L" and firstMove == "H->L"
        delta1 := -2
    else
        delta1 := 0
    movementScore := movementScore[1] + delta1

// --- Determine Bar State and Trend for SS Score ---
// Here we define a result category:
// 3 = "HHLL" (both higher high and lower low)
// 2 = "HH"  (higher high only)
// 1 = "LL"  (lower low only)
// 0 = "NA"  (neither)
bool newHigh = high > high[1] and low > low[1]
bool newLow  = low < low[1] and high < high[1]
bool both = high > high[1] and low < low[1]
bool none = high < high[1] and low > low[1]

string currentOrder2 = newHigh ? "HH" : newLow ? "LL" : both ? "HHLL" : "NA"

var float hhllScore = 0.0
var int delta2 = 0
string prevOrder2 = currentOrder2[1]

if bar_index > 0
    if na(prevOrder2) or na(currentOrder2)
        delta2 := 0
    else if prevOrder2 == "HH" and currentOrder2 == "HH"
        delta2 := 2
    else if (prevOrder2 == "LL" or prevOrder2 == "HHLL" or prevOrder2 == "NA") and currentOrder2 == "HH"
        delta2 := 1
    else if (prevOrder2 == "HH" or prevOrder2 == "HHLL" or prevOrder2 == "NA") and currentOrder2 == "LL"
        delta2 := -1
    else if prevOrder2 == "LL" and currentOrder2 == "LL"
        delta2 := -2
    else
        delta2 := 0
    hhllScore := hhllScore[1] + delta2


// --- Calculations: ATR and Modified RSI ---
tr  = math.max(high - low, math.abs(high - close[1]), math.abs(low - close[1]))
atr = ta.sma(tr, atrLength)

delta    = close - close[1]
//gain     = (delta > 0 and delta > smoothness * atr and delta > nz(delta[1], 0)) ? delta : 0
//loss     = (delta < 0 and math.abs(delta) > smoothness * atr and math.abs(delta) > math.abs(nz(delta[1], 0))) ? math.abs(delta) : 0
gain     = delta > 0 ? delta : 0
loss     = delta < 0 ? math.abs(delta) : 0
avg_gain = ta.sma(gain, rsiLength)
avg_loss = ta.sma(loss, rsiLength)
rs  = avg_loss != 0 ? avg_gain / avg_loss : 0
rsi = avg_loss == 0 ? (avg_gain == 0 ? 50 : 100) : (100 - 100 / (1 + rs))

// --- Compute Cumulative RSI Score ---
var float rsiScore = -50.0
var int delta3 = 0
float prevRS = nz(rsiScore[1], 0)

if bar_index > 0
    if rsi >= rsiMidLowThresh and rsi <= rsiMidHighThresh
        rsiScore := -50
    else if rsi > rsiHighThresh
        rsiScore := prevRS + 2
    else if rsi > rsiMidHighThresh and rsi <= rsiHighThresh 
        rsiScore := prevRS + 1
    else if rsi > rsiLowThresh and rsi <= rsiMidLowThresh
        rsiScore := prevRS - 1
    else if rsi < rsiLowThresh
        rsiScore := prevRS - 2

// --- Calculate Directional Change Score ---
bool Up = close > close[1]
bool Dn = close < close[1]

string currentOrder3 = Up ? "U" : Dn ? "D" : "NA"

var float udScore = 0.0
var int delta4 = 0
string prevOrder3 = currentOrder3[1]

if bar_index > 0
    if na(prevOrder3) or na(currentOrder3)
        delta4 := 0
    else if prevOrder3 == "U" and currentOrder3 == "U"
        delta4 := 2
    else if prevOrder3 == "D" and currentOrder3 == "U"
        delta4 := 1
    else if prevOrder3 == "U" and currentOrder3 == "D"
        delta4 := -1
    else if prevOrder3 == "D" and currentOrder3 == "D"
        delta4 := -2
    else
        delta4 := 0
    udScore := udScore[1] + delta4

// Cumulative % move score
var float changes = 0.0
changes := ((close - close[1]) / close[1]) * 100

var float dpercScore = 0.0
var int delta5 = 0

if bar_index > 0
    if changes >= 0 and changes > math.abs(changes[1])
        delta5 := 2
    else if changes >= 0 and changes < math.abs(changes[1])
        delta5 := 1
    else if changes < 0 and math.abs(changes) < math.abs(changes[1])
        delta5 := -1
    else if changes < 0 and math.abs(changes) > math.abs(changes[1])
        delta5 := -2
    else
        delta5 := 0
    dpercScore := dpercScore[1] + delta5

// First calculate the levels
highLookback = ta.highest(high, lookbackPeriod)[6]
lowLookback = ta.lowest(low, lookbackPeriod)[6]

dist = highLookback - lowLookback
lowdist = lowLookback + 0.236 * dist
higdist = highLookback - 0.236 * dist
middist = lowLookback + (dist / 2)

// Calculate signal first (equivalent to Python signal calculation)
var float signal = 0.0
if close > highLookback
    signal := 3
else if close <= highLookback and high > highLookback
    signal := 2
else if close < highLookback and close >= higdist and high <= highLookback
    signal := 1
else if close < higdist and close > lowdist
    signal := 0
else if close > lowLookback and close <= lowdist and low >= lowLookback
    signal := -1
else if close >= lowLookback and low < lowLookback
    signal := -2
else if close < lowLookback
    signal := -3
else
    signal := 0

// Calculate dc_score using signal (equivalent to Python dc_score calculation)
var float dcScore = 50.0
var float prevDcScore = 0.0
var float prevSignal = na

if bar_index > 0
    prevDcScore := dcScore
    prevSignal := nz(signal[1])
    
    if signal >= prevSignal and signal != 0 and prevSignal >= 0
        dcScore := prevDcScore + 1
    else if signal <= prevSignal and signal != 0 and prevSignal <= 0
        dcScore := prevDcScore - 1
    else if signal == 0
        dcScore := 50
    else
        dcScore := prevDcScore

// --- Calculate SS Score ---
// Calculate state based on order, direction and result
var int stateVal = 0

// Map states like in the Python code:
// L->H + U + HH = 8
// L->H + U + nonHH/HHLL = 7
// L->H + D + HH = 6
// L->H + D + nonHH/HHLL = 5
// H->L + U + nonLL/HHLL = 4
// H->L + U + LL = 3
// H->L + D + nonHH/HHLL = 2
// H->L + D + LL = 1
// else 0
if bar_index > 0
    if firstMove == "L->H"
        if currentOrder3 == "U"
            if currentOrder2 == "HH"
                stateVal := 8
            else  // nonHH/HHLL
                stateVal := 7
        else if currentOrder3 == "D"
            if currentOrder2 == "HH"
                stateVal := 6
            else  // nonHH/HHLL
                stateVal := 5
    else if firstMove == "H->L"
        if currentOrder3 == "U"
            if currentOrder2 == "LL"
                stateVal := 3
            else  // nonLL/HHLL
                stateVal := 4
        else if currentOrder3 == "D"
            if currentOrder2 == "LL"
                stateVal := 1
            else  // nonLL/HHLL
                stateVal := 2
    else
        stateVal := 0

// Calculate rolling state score (ss)
// state 8: ss += 3
// state 7: ss += 2
// state 6: ss += 1
// state 3: ss -= 1
// state 2: ss -= 2
// state 1: ss -= 3
// states 4, 5, 0: no change

// Replace your ssScore calculation with:
var float ssScore = 0.0
prevSS = ssScore[1]

if bar_index > 0    
    // Only apply normal calculations if we're not on first visible bar
    if stateVal == 8
        ssScore := prevSS + 10
    else if stateVal == 7
        ssScore := prevSS + 3
    else if stateVal == 6
        ssScore := prevSS + 1
    else if stateVal == 3
        ssScore := prevSS - 1
    else if stateVal == 2
        ssScore := prevSS - 3
    else if stateVal == 1
        ssScore := prevSS - 10
    else  // states 4, 5, 0
        ssScore := 0
        //ssScore := prevSS

    // Reset ssScore at weekly open for timeframes less than daily
    if timeframe.isintraday and dayofweek == dayofweek.sunday and hour == 17 and minute == 0
        ssScore := 0

// Create custom series based on close price comparison
var float customSeries = na
//customSeries := na(customSeries[1]) ? 0 : customSeries[1] + (close > close[1] ? seriesIncrement : -seriesIncrement)

flot = 0 //customSeries //ta.sma(customSeries, 6)

drop = (open[1] < middist[1] or close[1] < middist[1]) and open < lowdist
drop1 = open < middist
rise = (open[1] > middist[1] or close[1] > middist[1]) and open > higdist
rise1 =  open > middist
exceedu = (close[1] < highLookback[1] and close > highLookback) or (close[1] > highLookback[1] and close > highLookback) // and close < hhig
exceedd = (close[1] > lowLookback[1] and close < lowLookback) or (close[1] < lowLookback[1] and close < lowLookback) // and close > llow

plot(flot, title = "deezcrap", color = exceedu ? color.new(color.white, 0) : exceedd ? color.new(color.white, 0) : rise ? #0000ff : drop ? #ff0000 : rise1 ? color.rgb(48, 162, 255) : drop1 ? color.rgb(255, 112, 112) : na, linewidth = 2, offset = 1)

// Plot the ss score along with other scores
plot(ssScore, color=ssScore>ssScore[1] ? color.blue: ssScore<ssScore[1] ? color.red : ssScore == 0 ? color.yellow : color.white, title="SS Score", linewidth=3)
plot(rsiScore, color=color.orange, title="RSI Score", linewidth=2)
plot(dcScore, color=color.gray, title="DC Score", linewidth=2)
//plot(time >= twoDaysAgo ? movementScore : na, color=color.white)
//plot(time >= twoDaysAgo ? hhllScore : na, color=color.green)
//plot(time >= twoDaysAgo ? udScore : na, color=color.purple)

hline(-50, "RSI Line", color=color.orange, linestyle=hline.style_dotted, linewidth=1)
hline(50, "DC Line", color=color.gray, linestyle=hline.style_dotted, linewidth=1)

// Add debug plots
//plot(time >= twoDaysAgo ? delta1 : na, title="Delta1", color=color.aqua)
//bgcolor(close[1] < ta.ema(close, 20) and firstMove == "H->L" ? color.new(color.fuchsia, 60) : close[1] > ta.ema(close, 20) and firstMove == "L->H" ? color.new(color.lime, 60) : firstMove == "H->L" ? color.new(color.red, 75) : firstMove == "L->H" ? color.new(color.green, 75) : na) //ssScore == 0 ? color.white : na)
bgcolor(rsiScore > -50 and dcScore > 50 ? color.new(color.blue, 50) : rsiScore < -50 and dcScore < 50 ? color.new(color.red, 50) : (rsiScore > -50 and dcScore == 50) or (rsiScore == -50 and dcScore > 50) ? color.new(color.aqua, 50) : (rsiScore < -50 and dcScore == 50) or (rsiScore == -50 and dcScore < 50) ? color.new(color.fuchsia, 50) : rsiScore < -50 and dcScore > 50 ? color.new(color.orange, 50) : rsiScore > -50 and dcScore < 50 ? color.new(color.lime, 50) : color.new(color.gray, 75))
// Add vertical white lines when ssScore equals 0
if ssScore == 0 and ssScore[1] != 0
    line.new(bar_index, 20, bar_index, -20, color=color.white, width=1)