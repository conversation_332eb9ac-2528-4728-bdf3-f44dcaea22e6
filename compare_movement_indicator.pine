//@version=6
indicator("Major Currencies Movement Comparison", overlay=false)

// Define the currency pairs
currencyPairs = array.new_string(7)
array.set(currencyPairs, 0, "FX:EURUSD")
array.set(currencyPairs, 1, "FX:GBPUSD")
array.set(currencyPairs, 2, "FX:AUDUSD")
array.set(currencyPairs, 3, "FX:NZDUSD")
array.set(currencyPairs, 4, "FX:USDCAD")
array.set(currencyPairs, 5, "FX:USDCHF")
array.set(currencyPairs, 6, "FX:USDJPY")

// getDailyData remains unchanged:
getDailyData(pair) =>
    atr21 = ta.atr(21)
    [dOpen, dHigh, dLow, dClose, dATR] = request.security(pair, "D", [open, high, low, close, atr21], lookahead=barmerge.lookahead_on)
    [dOpen, dHigh, dLow, dClose, dATR]

// Existing calculateMovement (for current day) stays as is:
calculateMovement(pair) =>
    [dPrevOpen, _, _, dPrevClose, _] = getDailyData(pair)
    math.abs((dPrevClose - dPrevOpen) / dPrevOpen * 100)

// New function to calculate previous day's movement using offset [1]
calculateMovementPrev(pair) =>
    [dOpen, _, _, dClose, _] = getDailyData(pair)
    math.abs((dClose[1] - dOpen[1]) / dOpen[1] * 100)

// Update calculateMovement to use previous day's data and new formula: (close - open)/open
calculateDRange(pair) =>
    [_, dPrevHigh, dPrevLow, _, _] = getDailyData(pair)
    perc = dPrevHigh - dPrevLow

// Update SMA calculations to use previous day's movement:
percEUR = ta.sma(calculateMovementPrev(array.get(currencyPairs, 0)), 72)
percGBP = ta.sma(calculateMovementPrev(array.get(currencyPairs, 1)), 72)
percAUD = ta.sma(calculateMovementPrev(array.get(currencyPairs, 2)), 72)
percNZD = ta.sma(calculateMovementPrev(array.get(currencyPairs, 3)), 72)
percCAD = ta.sma(calculateMovementPrev(array.get(currencyPairs, 4)), 72)
percCHF = ta.sma(calculateMovementPrev(array.get(currencyPairs, 5)), 72)
percJPY = ta.sma(calculateMovementPrev(array.get(currencyPairs, 6)), 72)

cMoves = array.new_float(7)
array.set(cMoves, 0, percEUR)
array.set(cMoves, 1, percGBP)
array.set(cMoves, 2, percAUD)
array.set(cMoves, 3, percNZD)
array.set(cMoves, 4, percCAD)
array.set(cMoves, 5, percCHF)
array.set(cMoves, 6, percJPY)

// Update isPositive to use previous day's data
isPositive(pair) =>
    [prevOpen, _, _, prevClose, _] = getDailyData(pair)
    prevClose >= prevOpen

// Create table
var table movementTable = table.new(position.top_right, 6, array.size(currencyPairs) + 1, border_width = 1)

// Set table headers
table.cell(movementTable, 0, 0, "Pair", bgcolor=color.gray)
table.cell(movementTable, 1, 0, "Price", bgcolor=color.gray)
table.cell(movementTable, 2, 0, "0D R vs 21D R", bgcolor=color.gray)
table.cell(movementTable, 3, 0, "0D / 10D % Move", bgcolor=color.gray)
table.cell(movementTable, 4, 0, "Ratio", bgcolor=color.gray)
table.cell(movementTable, 5, 0, "+/- Day", bgcolor=color.gray)

// Loop through each currency pair and populate the table
for i = 0 to array.size(currencyPairs) - 1
    pair = array.get(currencyPairs, i)
    move = array.get(cMoves, i)
    // Extract daily data once per pair
    [dOpen, dHigh, dLow, dClose, dATR] = getDailyData(pair)
    // Calculate values using the extracted data
    movementPrev1 = (math.abs((dClose - dOpen) / dOpen * 100))// Prev day % move
    movementPrev  = dHigh - dLow                             // Prev day range
    positivePrev  = dClose >= dOpen ? "+" : "-"

    isJPY = pair == "FX:USDJPY" ? 100 : 10000
    
    table.cell(movementTable, 0, i + 1, str.substring(pair, 3, str.length(pair)), bgcolor = positivePrev == "+" ? color.green : color.red)
    table.cell(movementTable, 1, i + 1, str.tostring(dClose, pair == "FX:USDJPY" ? "#.###" : "#.#####") + " (" + str.tostring((dClose - dOpen) * isJPY, "#.##") + ")", bgcolor = positivePrev == "+" ? color.green : color.red)
    table.cell(movementTable, 2, i + 1, str.tostring(movementPrev / dATR * 100, "#.#") + "% (" + str.tostring(dATR * isJPY, "#.#") + ")", bgcolor = positivePrev == "+" ? color.green : color.red)
    table.cell(movementTable, 3, i + 1, str.tostring(movementPrev1 / move * 100, "#.#") + "% ( " + str.tostring(move, "#.###") + ")", bgcolor = positivePrev == "+" ? color.green : color.red)
    table.cell(movementTable, 4, i + 1, str.tostring(((movementPrev / dATR * 100) / (movementPrev1 / move * 100)), "#.##"), bgcolor = positivePrev == "+" ? color.green : color.red)
    table.cell(movementTable, 5, i + 1, positivePrev, bgcolor = positivePrev == "+" ? color.green : color.red)

// ----- New Code: Five‑Minute Movement and Correlations -----
// New function to compute 5-min movement series for a given pair
f_movement(pair) =>
    request.security(pair, "5", ((close - open) / open * 100), lookahead=barmerge.lookahead_on)

// Remove individual fMovement variables and replace with function calls:
fMovement0 = f_movement(array.get(currencyPairs, 0))
fMovement1 = f_movement(array.get(currencyPairs, 1))
fMovement2 = f_movement(array.get(currencyPairs, 2))
fMovement3 = f_movement(array.get(currencyPairs, 3))
fMovement4 = -f_movement(array.get(currencyPairs, 4))
fMovement5 = -f_movement(array.get(currencyPairs, 5))
fMovement6 = -f_movement(array.get(currencyPairs, 6))

// Compute the bar index at day start and number of bars since the day began
var int dayStart = bar_index
if ta.change(time("D")) != 0
    dayStart := bar_index
lenDay = bar_index - dayStart

// Create a new table for correlation matrix (8x8: first row/column are headers)
var table corrTable = table.new(position.bottom_right, 8, 8, border_width = 1)
table.cell(corrTable, 0, 0, "Curr", bgcolor=color.gray)
// Set header labels from the currency pair symbols (trim "FX:" prefix)
for j = 0 to 6
    curLabel = str.substring(array.get(currencyPairs, j), 3, str.length(array.get(currencyPairs, j)))
    table.cell(corrTable, j + 1, 0, curLabel, bgcolor=color.gray)
    table.cell(corrTable, 0, j + 1, curLabel, bgcolor=color.gray)

// Compute and fill correlation values using the number of 5-min bars since day start (lenDay)
for i = 0 to 6
    for j = 0 to 6
        // Select movement series for pair i and pair j using previously defined fMovement variables
        mov_i = i == 0 ? fMovement0 : i == 1 ? fMovement1 : i == 2 ? fMovement2 : i == 3 ? fMovement3 : i == 4 ? fMovement4 : i == 5 ? fMovement5 : fMovement6
        mov_j = j == 0 ? fMovement0 : j == 1 ? fMovement1 : j == 2 ? fMovement2 : j == 3 ? fMovement3 : j == 4 ? fMovement4 : j == 5 ? fMovement5 : fMovement6
        corrValue = ta.correlation(mov_i, mov_j, 20)
        table.cell(corrTable, j + 1, i + 1, str.tostring(corrValue, "#.##"))
