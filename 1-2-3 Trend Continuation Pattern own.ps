// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © ThiagoSchmitz

// Description
// This indicator is based on the 123 Pattern price continuation
// it will place entry, stop loss and take profit prices for reference

// The Take Profit Multiplicator parameter will be aplied at the distance between the first and second point.
// Stop Loss vlaue will always be the distance between first and second point
// The other configuration is the High/Low length. The same way you can find it on Zig-zag indicator,
// it will be the range used on the calculation. The last paramter will be an option to show long/short orders

//@version=5
indicator("123 Trend Continuation Pattern", overlay=true, max_lines_count=500, max_labels_count=500)

// Inputs
float tp = input.float(2, "Take Profit Multiplicator")
int length = input.int(5, "High/Low length")
string tradetype = input.string("Long and Short", "Show Orders", ["Long and Short", "Long", "Short"])
string textSize = input.string(size.small, "Text Size", [size.huge, size.large, size.normal, size.small, size.tiny, size.auto])
color takeProfitColor = input.color(color.green, "Take Profit Color")
color stopLossColor = input.color(color.red, "Stop Loss Color")
bool useCloseCandle = input.bool(false, "Use Close Candle's Value for Entry")

// Declarations
float h = ta.highest(high, length * 2 + 1)
float l = ta.lowest(low, length * 2 + 1)
f_isMin(len) => l == low[len]
f_isMax(len) => h == high[len]
bool recentTouch = false

// Variables
var bool dirUp = false
var float lastLow = high * 100
var float lastHigh = 0.0
var int timeLow = bar_index
var int timeHigh = bar_index
var line li = na
bool isMin = f_isMin(length)
bool isMax = f_isMax(length)

// Functions
f_drawLine() => line.new(timeHigh - length, lastHigh, timeLow - length, lastLow, xloc.bar_index, color=color.gray, width=1 )

// Direction
if dirUp
    if isMin and low[length] < lastLow
        lastLow := low[length]
        timeLow := bar_index
        line.delete(li)
        li := f_drawLine()

    if isMax and high[length] > lastLow
        lastHigh := high[length]
        timeHigh := bar_index
        dirUp := false
        li := f_drawLine()

if not dirUp
    if isMax and high[length] > lastHigh
        lastHigh := high[length]
        timeHigh := bar_index
        line.delete(li)
        li := f_drawLine()
    if isMin and low[length] < lastHigh
        lastLow := low[length]
        timeLow := bar_index
        dirUp := true
        li := f_drawLine()
        if (isMax and high[length] > lastLow)
            lastHigh := high[length]
            timeHigh := bar_index
            dirUp := false
            li := f_drawLine()

// Checkers        
for int i = 1 to 10
    if (low[i] <= lastLow[i] and low[i + 1] > lastLow[i + 1]) or (high[i] >= lastHigh[i] and high[i + 1] < lastHigh[i + 1])
        recentTouch := true
        break

// Conditions Definiitions
longCondition = (useCloseCandle ? close : high) >= lastHigh and high[1] < lastHigh[1] and not recentTouch and (tradetype == "Long and Short" or tradetype == "Long")
shortCondition = (useCloseCandle ? close : low) <= lastLow and low[1] > lastLow[1] and not recentTouch and (tradetype == "Long and Short" or tradetype == "Short")

// Plots
plotshape(shortCondition and not longCondition ? -1 : 0, "", style= shape.triangledown, location = location.abovebar, color = color.maroon, size = size.tiny)
plotshape(longCondition and not shortCondition ? 1 : 0, "", style= shape.triangleup, location = location.belowbar, color = color.navy, size = size.tiny)

//plotarrow(shortCondition and not longCondition ? -1 : 0, "", color.green, color.red, maxheight=20)
//plotarrow(longCondition and not shortCondition ? 1 : 0, "", color.green, color.red, maxheight=20)

//Alerts
alertcondition(shortCondition and not longCondition, "123 Trend Continuation Short", "Short")
alertcondition(longCondition and not shortCondition, "123 Trend Continuation Long", "Long")
alertcondition(ta.crossover(high, lastHigh + (lastHigh - lastLow) * tp), "123 Trend Continuation Long Take Profit", "Take Profit")
alertcondition(ta.crossunder(low, lastLow), "123 Trend Continuation Long Stop Loss", "Stop Loss")
alertcondition(ta.crossunder(low, lastLow - (lastHigh - lastLow) * tp), "123 Trend Continuation Short Take Profit", "Take Profit")
alertcondition(ta.crossover(high, lastHigh), "123 Trend Continuation Short Stop Loss", "Stop Loss")

//Conditions
if shortCondition
    label.new(bar_index - 2, lastLow, "Short (" + str.tostring(lastLow, "#.####") + ")", xloc.bar_index, yloc.price, color.maroon, label.style_none, color.maroon, textSize, text.align_right)
    label.new(bar_index - 2, lastLow - (lastHigh - lastLow) * tp, "TP2 " + str.tostring(lastLow - (lastHigh - lastLow) * tp, "#.####") + ")", xloc.bar_index, yloc.price, color.orange, label.style_none, color.orange, textSize, text.align_right)
    label.new(bar_index - 2, lastLow - ((lastHigh - lastLow) * tp) / 2, "TP1 " + str.tostring(lastLow - ((lastHigh - lastLow) * tp) / 2, "#.####") + ")", xloc.bar_index, yloc.price, color.red, label.style_none, color.red, textSize, text.align_right)
    label.new(bar_index - 2, lastHigh, "SL (" + str.tostring(lastHigh, "#.####") + ")", xloc.bar_index, yloc.price, color.maroon, label.style_none, color.maroon, textSize, text.align_right)
    line.new(timeLow - length, lastLow, bar_index, lastLow, xloc.bar_index, color=color.maroon, width=2)
    line.new(timeHigh - length, lastHigh, bar_index, lastHigh, xloc.bar_index, color=color.maroon, width=1)
    line.new(timeLow - length, lastLow - (lastHigh - lastLow) * tp, bar_index, lastLow - (lastHigh - lastLow) * tp, xloc.bar_index, color=color.orange, width=1, style=line.style_dashed)
    line.new(timeLow - length, lastLow - ((lastHigh - lastLow) * tp) / 2, bar_index, lastLow - ((lastHigh - lastLow) * tp) / 2, xloc.bar_index, color=color.red, width=1, style=line.style_dashed)

if longCondition and not shortCondition
    line.new(timeHigh - length, lastHigh, bar_index, lastHigh, xloc.bar_index, color=color.navy, width=2)
    line.new(timeLow - length, lastLow, bar_index, lastLow, xloc.bar_index, color=color.navy, width=1)
    line.new(timeHigh - length, lastHigh + (lastHigh - lastLow) * tp, bar_index, lastHigh + (lastHigh - lastLow) * tp, xloc.bar_index, color=color.rgb(0, 200, 226), width=1, style=line.style_dashed)
    line.new(timeHigh - length, lastHigh + ((lastHigh - lastLow) * tp) / 2, bar_index, lastHigh + ((lastHigh - lastLow) * tp) / 2, xloc.bar_index, color=color.blue, width=1, style=line.style_dashed)
    label.new(bar_index - 2, lastHigh, "Long (" + str.tostring(lastHigh, "#.####") + ")", xloc.bar_index, yloc.price, color.navy, label.style_none, color.navy, textSize, text.align_right)
    label.new(bar_index - 2, lastLow, "SL (" + str.tostring(lastLow, "#.####") + ")", xloc.bar_index, yloc.price, color.navy, label.style_none, color.navy, textSize, text.align_right)
    label.new(bar_index - 2, lastHigh + ((lastHigh - lastLow) * tp) / 2, "TP1 (" + str.tostring(lastHigh + ((lastHigh - lastLow) * tp) / 2, "#.####") + ")", xloc.bar_index, yloc.price, color.blue, label.style_none, color.blue, textSize, text.align_right)
    label.new(bar_index - 2, lastHigh + (lastHigh - lastLow) * tp, "TP2 (" + str.tostring(lastHigh + (lastHigh - lastLow) * tp, "#.####") + ")", xloc.bar_index, yloc.price, color.rgb(0, 200, 226), label.style_none, color.rgb(0, 200, 226), textSize, text.align_right)
