//@version=6
indicator("deezcrap_u")

// Input fields for customization
lookbackPeriod = input.int(120, title="Lookback Period for High/Low")
seriesIncrement = input.float(0.001, title="Series Increment/Decrement Value")

// Create custom series based on close price comparison
var float customSeries = na
customSeries := na(customSeries[1]) ? 0 : customSeries[1] + (close > close[1] ? seriesIncrement : -seriesIncrement)

flot = 0 //customSeries //ta.sma(customSeries, 6)

// Calculate high and low over the lookback period
highLookback = ta.highest(high, lookbackPeriod)[6]
lowLookback = ta.lowest(low, lookbackPeriod)[6]

dist = highLookback - lowLookback
lowdist = lowLookback + 0.236 * dist
higdist = highLookback - 0.236 * dist
middist = lowLookback + (dist / 2)

if close < middist
    flot -= 0.001
else if close > middist
    flot += 0.001
else
    flot := 0

drop = (open[1] < middist[1] or close[1] < middist[1]) and open < lowdist
drop1 = open < middist
rise = (open[1] > middist[1] or close[1] > middist[1]) and open > higdist
rise1 =  open > middist
exceedu = (close[1] < highLookback[1] and close > highLookback) or (close[1] > highLookback[1] and close > highLookback) // and close < hhig
exceedd = (close[1] > lowLookback[1] and close < lowLookback) or (close[1] < lowLookback[1] and close < lowLookback) // and close > llow

//plot(flot, title = "deezcrap", color = exceedu ? color.new(color.green, 0) : exceedd ? color.new(color.orange, 0) : rise ? #0000ff : drop ? #ff0000 : rise1 ? color.rgb(48, 162, 255) : drop1 ? color.rgb(255, 112, 112) : color.white, linewidth = 5, offset = 1)
plot(flot, title = "deezcrap", color = rise1 ? #0000ff : drop1 ? #ff0000 : color.white, linewidth = 5, offset = 1)
//plot(flotup, title = "deezcrapU", color = flotupc ? color.white : color.blue)
//plot(flotdn, title = "deezcrapD", color = flotdnc ? color.white : color.red)