//+------------------------------------------------------------------+
//|  DeezNuts_Histogram.mq4                                          |
//|  Blue bars when (FastMA-SlowMA) > 0, Red bars when ≤ 0           |
//+------------------------------------------------------------------+
#property strict
#property indicator_chart_window
#property indicator_buffers   2
//#property indicator_minimum   -0.02          // scale helpers (adjust)
//#property indicator_maximum    0.02
#property indicator_color1     Green          // up-osc > 0
#property indicator_color2     Violet           // down-osc ≤ 0

//─── user inputs ────────────────────────────────────────────────────
extern int FastLength   = 1;     // fast MA period (≥1)
extern int SlowLength   = 5;     // slow MA period (> FastLength)
extern int SmoothLength = 3;     // Wilder smoothing of (fast-slow); 1 = off
extern int MAMethod     = 1;     // 0=sma 1=ema 2=smma 3=lwma

//─── visible buffers ───────────────────────────────────────────────
double upHist[], dnHist[];

//─── work array for the raw oscillator (not plotted) ───────────────
double rawOsc[];

//+------------------------------------------------------------------+
//|  Indicator initialisation                                         |
//+------------------------------------------------------------------+
int OnInit()
{
   // reserve extra buffers for internal work
   IndicatorBuffers(3);

   // bind visible buffers
   SetIndexStyle (0, DRAW_HISTOGRAM, STYLE_SOLID, 2, Green);  // up bars
   SetIndexBuffer(0, upHist);
   SetIndexLabel (0, "Osc > 0");

   SetIndexStyle (1, DRAW_HISTOGRAM, STYLE_SOLID, 2, Violet);   // down bars
   SetIndexBuffer(1, dnHist);
   SetIndexLabel (1, "Osc ≤ 0");

   // create work array
   ArrayResize(rawOsc, Bars);
   ArraySetAsSeries(rawOsc, true);

   IndicatorShortName("DeezNuts Histogram  (" + IntegerToString(FastLength)
                      + "," + IntegerToString(SlowLength) + ")");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//|  Main calculation loop                                            |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double   &open[],
                const double   &high[],
                const double   &low[],
                const double   &close[],
                const long     &tick_volume[],
                const long     &volume[],
                const int      &spread[])
{
   // oldest bar we must refresh
   int start = (prev_calculated == 0) ? (SlowLength + SmoothLength + 5)
                                      : prev_calculated - 1;

   static datetime starting = 0;
   bool check = false;
   if (starting < iTime(_Symbol, PERIOD_M1, 0))
   {
      starting = iTime(_Symbol, PERIOD_M1, 0);
      check = true;
   }
   if (check)
   {   
   for(int i = 500; i >= 0; i--)
   {
      //── 1. raw oscillator = fastMA – slowMA
      double fast = iMA(NULL, 0, FastLength , 0, MAMethod, PRICE_OPEN, i);
      double slow = iMA(NULL, 0, SlowLength, 0, MAMethod, PRICE_OPEN, i);
      rawOsc[i]   = fast - slow;

      //── 2. optional Wilder smoothing (SMMA)
      double osc = (SmoothLength <= 1)
                   ? rawOsc[i]
                   : iMAOnArray(rawOsc, 0, SmoothLength, 0, MODE_SMMA, i);

      //── 3. paint into one of the two histogram buffers
      if(osc > 0.0 && close[i] > open[i])
      {
         upHist[i] = close[i];
         dnHist[i] = open[i];     // hide opposite colour
      }
      else if(osc > 0.0 && open[i] > close[i])
      {
         upHist[i] = open[i];
         dnHist[i] = close[i];     // hide opposite colour
      }
      else if(osc <= 0.0 && open[i] > close[i])
      {
         upHist[i] = close[i];
         dnHist[i] = open[i];     // hide opposite colour
      }
      else if(osc <= 0.0 && close[i] > open[i])
      {
         upHist[i] = open[i];
         dnHist[i] = close[i];     // hide opposite colour
      }
   }
   }
   return(rates_total);
}
//+------------------------------------------------------------------+
